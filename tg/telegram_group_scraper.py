#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
import os
import random
import traceback
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any, Set
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from threading import Lock
from upload import upload_file, prefix

from telethon import TelegramClient, errors
from telethon.tl.types import (
    Channel, User, MessageService, Chat, 
    PeerChannel, PeerUser, PeerChat
)

# 配置参数
MSG_LIMIT = 3500  # 减少消息数量用于测试
TG_ACCOUNT_DIR = "tg_account_0808_50"
MAX_RETRIES_PER_CHANNEL = 2  # 每个频道最多重试2次
THREAD_COUNT = 10  # 减少线程数量用于测试

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_group_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ChannelStatus:
    """频道状态跟踪"""
    name: str
    tried_accounts: Set[str] = None
    found: bool = False
    completed: bool = False
    retry_count: int = 0
    in_progress: bool = False  # 是否正在处理中

    def __post_init__(self):
        if self.tried_accounts is None:
            self.tried_accounts = set()

class AccountPool:
    """账户池管理器"""
    
    def __init__(self, account_dir: str = TG_ACCOUNT_DIR):
        self.account_dir = Path(f"account/{TG_ACCOUNT_DIR}")
        self.accounts = []
        self.account_lock = Lock()
        self.load_accounts()
    
    def load_accounts(self):
        self.accounts = [
            {
                'id': '+*************',
                'phone': '+*************',
                'app_id': ********,
                'app_hash': 'ed3e2291059b5e3c9d583a323956efc8',
                'session_file': 'stable/+*************.session',
                'twoFA': '',
                'proxy': {
                    'proxy_type': 'http',
                    'addr': "6zu4wmwq3hepp-as.ipidea.online",
                    'port': 2333,
                    'username': "furion2025-zone-ytb2319538",
                    'password': "furion2025"
                },
                'in_use': False,
                'failure_count': 0
            },
            {
                'id': '+*************',
                'phone': '+*************',
                'app_id': ********,
                'app_hash': '43682ab7d51a8cf2ee69550ad90052b2',
                'session_file': 'stable/+*************.session',
                'twoFA': '',
                'proxy': {
                    'proxy_type': 'http',
                    'addr': "6zu4wmwq3hepp-as.ipidea.online",
                    'port': 2333,
                    'username': "furion2025-zone-ytb2319538",
                    'password': "furion2025"
                },
                'in_use': False,
                'failure_count': 0
            },
            {
                'id': '+8618061991139',
                'phone': '+8618061991139',
                'app_id': 27053382,
                'app_hash': '73940204450a7683bdcfb505037a5338',
                'session_file': 'stable/+8618061991139.session',
                'twoFA': '',
                'proxy': {
                    'proxy_type': 'http',
                    'addr': "6zu4wmwq3hepp-as.ipidea.online",
                    'port': 2333,
                    'username': "furion2025-zone-ytb2319538",
                    'password': "furion2025"
                },
                'in_use': False,
                'failure_count': 0
            }, 
            {
                'id': '+8618321260681',
                'phone': '+8618321260681',
                'app_id': 20044015,
                'app_hash': '84d9a7d82b4ff1cd095ff4bb21911cea',
                'session_file': 'stable/+8618321260681.session',
                'twoFA': '',
                'proxy': {
                    'proxy_type': 'http',
                    'addr': "6zu4wmwq3hepp-as.ipidea.online",
                    'port': 2333,
                    'username': "furion2025-zone-ytb2319538",
                    'password': "furion2025"
                },
                'in_use': False,
                'failure_count': 0
            }, 
            # {
            #     'id': '+*************',
            #     'phone': '+*************',
            #     'app_id': 25371857,
            #     'app_hash': '1db71230ae5bf5bfe51c395776b66764',
            #     'session_file': 'stable/+*************.session',
            #     'twoFA': '',
            #     'proxy': {
            #         'proxy_type': 'http',
            #         'addr': "6zu4wmwq3hepp-as.ipidea.online",
            #         'port': 2333,
            #         'username': "furion2025-zone-ytb2319538",
            #         'password': "furion2025"
            #     },
            #     'in_use': False,
            #     'failure_count': 0
            # }
        ]

    def load_accounts_ext(self):
        """加载所有可用的Telegram账号"""
        if not self.account_dir.exists():
            raise FileNotFoundError(f"账号目录不存在: {self.account_dir}")
            
        for account_path in self.account_dir.iterdir():
            if account_path.is_dir():
                # 查找JSON和session文件
                json_files = list(account_path.glob("*.json"))
                session_files = list(account_path.glob("*.session"))

                if json_files and session_files:
                    json_file = json_files[0]  # 取第一个JSON文件
                    session_file = session_files[0]  # 取第一个session文件
                
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            account_data = json.load(f)

                        account_info = {
                            'id': account_path.name,
                            'phone': account_data['phone'],
                            'app_id': account_data['app_id'],
                            'app_hash': account_data['app_hash'],
                            'session_file': str(session_file),
                            'twoFA': account_data.get('twoFA', ''),
                            'proxy': account_data.get('proxy'),
                            'status': account_data.get('status', 'unknown'),
                            'in_use': False,
                            'failure_count': 0
                        }
                        self.accounts.append(account_info)
                        logger.info(f"加载账号: {account_data['phone']}")
                    except Exception as e:
                        logger.error(f"加载账号配置失败 {json_file}: {e}")
        
        if not self.accounts:
            raise ValueError("未找到可用的Telegram账号")
        
        logger.info(f"总共加载了 {len(self.accounts)} 个账号")
    
    def get_available_account(self, exclude_ids: Set[str] = None) -> Optional[Dict[str, Any]]:
        """获取一个可用的账号"""
        with self.account_lock:
            exclude_ids = exclude_ids or set()
            
            # 优先选择未使用且失败次数少的账号
            available_accounts = [
                acc for acc in self.accounts 
                if not acc['in_use'] and acc['id'] not in exclude_ids
            ]
            
            if not available_accounts:
                return None
            
            # 按失败次数排序，选择失败次数最少的
            available_accounts.sort(key=lambda x: x['failure_count'])
            account = available_accounts[0]
            account['in_use'] = True
            return account
    
    def release_account(self, account_id: str):
        """释放账号"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['in_use'] = False
                    break
    
    def mark_account_failed(self, account_id: str):
        """标记账号失败"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['failure_count'] += 1
                    break

class ChannelPool:
    """频道池管理器"""
    
    def __init__(self, channels_file: str = 'channels/channels.txt'):
        self.channels_file = Path(channels_file)
        self.channels_status = {}
        self.channel_lock = Lock()
        self.load_channels()
        
    def load_channels(self):
        """加载频道列表"""
        if not self.channels_file.exists():
            raise FileNotFoundError(f"频道文件不存在: {self.channels_file}")
        
        with open(self.channels_file, 'r', encoding='utf-8') as f:
            channels = [line.strip() for line in f if line.strip()]
        
        # 检查已完成的频道
        completed_file = Path('channels/channels_crawled.txt')
        completed_channels = set()
        if completed_file.exists():
            with open(completed_file, 'r', encoding='utf-8') as f:
                completed_channels = {line.strip() for line in f if line.strip()}

        # 检查已完成的频道
        ignore_file = Path('channels/channels_ignore.txt')
        ignore_channels = set()
        if ignore_file.exists():
            with open(ignore_file, 'r', encoding='utf-8') as f:
                ignore_channels = {line.strip() for line in f if line.strip()}
        
        # 初始化频道状态
        for channel in channels:
            if channel not in completed_channels and channel not in ignore_channels:
                self.channels_status[channel] = ChannelStatus(name=channel)
        
        logger.info(f"加载了 {len(self.channels_status)} 个待处理频道")
    
    def get_next_channel(self) -> Optional[ChannelStatus]:
        """获取下一个需要处理的频道"""
        with self.channel_lock:
            # 找到未完成且重试次数未超限且未被占用的频道
            for channel_status in self.channels_status.values():
                if (not channel_status.completed and
                    channel_status.retry_count < MAX_RETRIES_PER_CHANNEL and
                    not getattr(channel_status, 'in_progress', False)):
                    # 标记为正在处理，防止其他线程重复获取
                    channel_status.in_progress = True
                    return channel_status
            return None
    
    def mark_channel_found(self, channel_name: str, account_id: str):
        """标记频道已找到"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].found = True
                self.channels_status[channel_name].tried_accounts.add(account_id)
    
    def mark_channel_not_found(self, channel_name: str, account_id: str):
        """标记频道在某个账号下未找到"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].tried_accounts.add(account_id)
                self.channels_status[channel_name].retry_count += 1
    
    def mark_channel_completed(self, channel_name: str):
        """标记频道处理完成"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].completed = True
                self.channels_status[channel_name].in_progress = False  # 释放处理状态

                # 写入完成文件
                with open('channels/channels_completed.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{channel_name}\n")
    
    def get_untried_accounts_for_channel(self, channel_name: str, all_account_ids: Set[str]) -> Set[str]:
        """获取某个频道未尝试过的账号ID列表"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                tried = self.channels_status[channel_name].tried_accounts
                return all_account_ids - tried
            return all_account_ids

    def release_channel(self, channel_name: str):
        """释放频道的处理状态"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].in_progress = False
    
    def mark_channel_retry_failed(self, channel_name: str):
        """标记频道重试失败，增加重试计数"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].retry_count += 1
                self.channels_status[channel_name].in_progress = False
                logger.info(f"频道 {channel_name} 重试失败，重试次数: {self.channels_status[channel_name].retry_count}")

class TelegramGroupScraper:
    """Telegram群组消息抓取器"""
    
    def __init__(self, account_pool: AccountPool, channel_pool: ChannelPool, worker_id: int):
        self.account_pool = account_pool
        self.channel_pool = channel_pool
        self.worker_id = worker_id
        self.current_client = None
        self.current_account = None
        
    async def create_client(self, account: Dict[str, Any]) -> Optional[TelegramClient]:
        """创建Telegram客户端"""
        try:
            # 代理配置
            proxy = None
            if account.get('proxy'):
                proxy_info = account['proxy']
                if len(proxy_info) >= 6:
                    proxy = {
                        'proxy_type': 'socks5',
                        'addr': proxy_info[1],
                        'port': proxy_info[2],
                        'username': proxy_info[4],
                        'password': proxy_info[5]
                    }
            
            client = TelegramClient(
                account['session_file'],
                account['app_id'],
                account['app_hash'],
                proxy=proxy
            )
            
            await client.start(phone=account['phone'])
            if not await client.is_user_authorized():
                logger.error(f"账号 {account['phone']} 未授权")
                return None
            
            # 测试连接
            me = await client.get_me()
            logger.info(f"Worker {self.worker_id}: 客户端创建成功 {me.phone}")
            return client
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 创建客户端失败 {account['phone']}: {e}")
            return None

    async def check_channel_exists(self, channel_name: str) -> bool:
        """检查频道是否存在"""
        if not self.current_client:
            return False

        try:
            entity = await self.current_client.get_entity(channel_name)
            return True
        except ValueError as e:
            if "No user has" in str(e) or "Could not find" in str(e):
                return False
            raise e
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 检查频道存在性失败 {channel_name}: {e}")
            return False

    def get_entity_type(self, entity) -> str:
        """获取实体类型"""
        if isinstance(entity, Channel):
            return "channel"
        elif isinstance(entity, Chat):
            return "group"
        elif isinstance(entity, User):
            return "bot" if entity.bot else "user"
        else:
            return "unknown"

    async def scrape_group_messages(self, channel_name: str) -> Optional[Dict]:
        """抓取群组消息"""
        if not self.current_client:
            return None

        try:
            entity = await self.current_client.get_entity(channel_name)

            messages_data = []
            message_count = 0
            message_len = 0

            # 获取最近的消息
            async for message in self.current_client.iter_messages(entity, limit=MSG_LIMIT):
                if message_count >= MSG_LIMIT:
                    break

                # 跳过服务消息
                if isinstance(message, MessageService):
                    continue

                sender_user = ""
                sender_username = ""
                entity_type = "unknown"

                if message.sender:
                    if hasattr(message.sender, 'first_name'):
                        sender_user = f"{message.sender.first_name or ''} {message.sender.last_name or ''}".strip()
                    if hasattr(message.sender, 'username') and message.sender.username:
                        sender_username = message.sender.username
                    entity_type = self.get_entity_type(message.sender)

                # 跳过频道自己发的消息
                if sender_username == channel_name.replace('@', ''):
                    continue

                # 格式化时间
                time_str = message.date.strftime('%Y-%m-%d %H:%M:%S') if message.date else ""

                if message.text:
                    message_data = {
                        "message_id": message.id,
                        "sender_user": sender_user,
                        "sender_username": sender_username,
                        "message": message.text or "",
                        "time": time_str,
                        "entity_type": entity_type
                    }

                    messages_data.append(message_data)
                    message_count += 1
                    message_len += len(message.text)

            if len(messages_data) <= 2:
                logger.warning(f"Worker {self.worker_id}: 频道 {channel_name} 消息数量过少: {len(messages_data)}")
                return None

            # 构造返回数据
            result = {
                "channel": channel_name,
                "total_messages": len(messages_data),
                "fetch_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "messages": messages_data
            }

            logger.info(f"Worker {self.worker_id}: 成功抓取群组 {channel_name}: {len(messages_data)} 条消息")
            return result

        except errors.ChannelPrivateError:
            logger.warning(f"Worker {self.worker_id}: 群组 {channel_name} 是私有群组")
            return None
        except errors.FloodWaitError as e:
            logger.warning(f"Worker {self.worker_id}: 触发速率限制，等待 {e.seconds} 秒")
            await asyncio.sleep(min(e.seconds, 300))  # 最多等待5分钟
            return None
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 抓取群组 {channel_name} 失败: {e}")
            return None

    async def save_messages(self, data: Dict, channel_name: str):
        """保存消息到文件"""
        try:
            os.makedirs('channel_messages', exist_ok=True)
            file_name = f"{channel_name.replace('@', '')}_messages.json"
            local_file = f"channel_messages/{file_name}"

            with open(local_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"Worker {self.worker_id}: 保存消息到文件 {local_file}")

            upload_file(local_file, f"{prefix}/{file_name}")

            # 记录统计信息
            with open("group_statistics.txt", "a", encoding='utf-8') as f:
                f.write(f"{channel_name},{data['total_messages']},{data['fetch_time']}\n")

        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 保存消息失败: {e}")

    async def process_channel_with_account_rotation(self, channel_status: ChannelStatus):
        """使用账号轮换处理频道"""
        channel_name = channel_status.name
        max_attempts = len(self.account_pool.accounts)  # 最多尝试所有账号
        attempts = 0

        while attempts < max_attempts:
            # 获取一个未尝试过该频道的可用账号
            account = self.account_pool.get_available_account(
                exclude_ids=channel_status.tried_accounts
            )

            if not account:
                logger.warning(f"Worker {self.worker_id}: 没有可用账号处理频道 {channel_name}")
                # 等待一下，可能有账号会被释放
                await asyncio.sleep(random.uniform(1, 3))
                attempts += 1
                continue

            try:
                # 创建客户端
                if self.current_account != account:
                    if self.current_client:
                        await self.current_client.disconnect()

                    self.current_client = await self.create_client(account)
                    self.current_account = account

                if not self.current_client:
                    self.account_pool.mark_account_failed(account['id'])
                    self.account_pool.release_account(account['id'])
                    # 标记该账号已尝试过（即使失败了）
                    self.channel_pool.mark_channel_not_found(channel_name, account['id'])
                    attempts += 1
                    continue

                # 检查频道是否存在
                if await self.check_channel_exists(channel_name):
                    logger.info(f"Worker {self.worker_id}: 在账号 {account['phone']} 中找到频道 {channel_name}")
                    self.channel_pool.mark_channel_found(channel_name, account['id'])

                    # 抓取消息
                    data = await self.scrape_group_messages(channel_name)
                    if data:
                        await self.save_messages(data, channel_name)
                        self.channel_pool.mark_channel_completed(channel_name)
                        logger.info(f"Worker {self.worker_id}: 完成处理频道 {channel_name}")
                        return True
                else:
                    logger.info(f"Worker {self.worker_id}: 在账号 {account['phone']} 中未找到频道 {channel_name}")
                    self.channel_pool.mark_channel_not_found(channel_name, account['id'])

                # 随机延迟
                await asyncio.sleep(random.uniform(2, 5))

            except Exception as e:
                logger.error(f"Worker {self.worker_id}: 处理频道 {channel_name} 时出错: {e}")
                self.account_pool.mark_account_failed(account['id'])
                # 即使出错也要标记该账号已尝试过
                self.channel_pool.mark_channel_not_found(channel_name, account['id'])
            finally:
                self.account_pool.release_account(account['id'])
                attempts += 1

        logger.warning(f"Worker {self.worker_id}: 尝试了 {attempts} 个账号都无法找到频道 {channel_name}")
        return False

    async def cleanup(self):
        """清理资源"""
        if self.current_client:
            await self.current_client.disconnect()
        if self.current_account:
            self.account_pool.release_account(self.current_account['id'])


async def worker_thread(account_pool: AccountPool, channel_pool: ChannelPool, worker_id: int):
    """工作线程函数"""
    scraper = TelegramGroupScraper(account_pool, channel_pool, worker_id)

    try:
        while True:
            # 获取下一个需要处理的频道
            channel_status = channel_pool.get_next_channel()
            if not channel_status:
                logger.info(f"Worker {worker_id}: 没有更多频道需要处理")
                break

            logger.info(f"Worker {worker_id}: 开始处理频道 {channel_status.name}")

            try:
                # 处理频道
                success = await scraper.process_channel_with_account_rotation(channel_status)

                if not success:
                    logger.warning(f"Worker {worker_id}: 频道 {channel_status.name} 处理失败")
                    # 增加重试计数，避免无限重试重试
                    channel_pool.mark_channel_retry_failed(channel_status.name)
            except Exception as e:
                logger.error(f"Worker {worker_id}: 处理频道 {channel_status.name} 时出错: {e}")
                # 出错时也要释放频道状态
                channel_pool.release_channel(channel_status.name)

            # 随机延迟，避免过于频繁的请求
            await asyncio.sleep(random.uniform(10, 20))

    except Exception as e:
        logger.error(f"Worker {worker_id}: 工作线程出错: {e}")
        logger.error(traceback.format_exc())
    finally:
        await scraper.cleanup()


async def main():
    """主函数"""
    try:
        logger.info("启动Telegram群组消息抓取器")

        # 创建必要的目录
        os.makedirs('channels', exist_ok=True)
        os.makedirs('channel_messages', exist_ok=True)

        # 初始化账户池和频道池
        account_pool = AccountPool()
        channel_pool = ChannelPool()

        if not account_pool.accounts:
            logger.error("没有可用的账号")
            return

        if not channel_pool.channels_status:
            logger.error("没有需要处理的频道")
            return

        logger.info(f"账户池: {len(account_pool.accounts)} 个账号")
        logger.info(f"频道池: {len(channel_pool.channels_status)} 个频道")

        # 创建工作线程
        thread_count = min(THREAD_COUNT, len(account_pool.accounts))
        tasks = []

        for i in range(thread_count):
            task = asyncio.create_task(worker_thread(account_pool, channel_pool, i + 1))
            tasks.append(task)
            logger.info(f"启动工作线程 {i + 1}")

        # 等待所有任务完成
        await asyncio.gather(*tasks)

        logger.info("所有抓取任务完成")

        # 输出统计信息
        completed_count = sum(1 for status in channel_pool.channels_status.values() if status.completed)
        total_count = len(channel_pool.channels_status)
        logger.info(f"处理完成: {completed_count}/{total_count} 个频道")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())
