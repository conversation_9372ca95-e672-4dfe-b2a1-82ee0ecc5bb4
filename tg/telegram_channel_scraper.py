#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any
import traceback
from concurrent.futures import ThreadPoolExecutor
import random
from upload import upload_file, list_files_with_prefix, bucket_name, prefix

from telethon import TelegramClient, errors
from telethon.tl.types import (
    Channel, User, MessageService, MessageMediaPhoto, MessageMediaDocument,
    PeerChannel, PeerUser, MessageEntityMention, MessageEntityMentionName
)
from telethon.tl.functions.contacts import ResolveUsernameRequest
from botocore.exceptions import NoCredentialsError, ClientError


MSG_LIMIT = 3500
tg_account = "tg_account_0807_2"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AccountManager:
    """Telegram账号管理器"""
    
    def __init__(self):
        self.account_dir = Path(f"account/{tg_account}")
        self.accounts = []
        self.current_account_index = 0
        self.load_accounts()
        
    def load_accounts(self):
        """加载所有可用的Telegram账号"""
        if not self.account_dir.exists():
            raise FileNotFoundError(f"账号目录不存在: {self.account_dir}")
            
        for account_path in self.account_dir.iterdir():
            if account_path.is_dir() and account_path.name.isdigit():
                json_file = account_path / f"{account_path.name}.json"
                session_file = account_path / f"{account_path.name}.session"
                
                if json_file.exists() and session_file.exists():
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            account_data = json.load(f)
                        
                        account_info = {
                            'phone': account_data['phone'],
                            'app_id': account_data['app_id'],
                            'app_hash': account_data['app_hash'],
                            'session_file': str(session_file),
                            'twoFA': account_data.get('twoFA', ''),
                            'proxy': account_data.get('proxy'),
                            'status': account_data.get('status', 'unknown'),
                            'failure_count': 0  # 连续失败次数
                        }
                        self.accounts.append(account_info)
                        logger.info(f"加载账号: {account_data['phone']}")
                    except Exception as e:
                        logger.error(f"加载账号配置失败 {json_file}: {e}")
        
        if not self.accounts:
            raise ValueError("未找到可用的Telegram账号")
        
        logger.info(f"总共加载了 {len(self.accounts)} 个账号 {tg_account}")
    
    def get_current_account(self) -> Dict[str, Any]:
        """获取当前账号"""
        return self.accounts[self.current_account_index]
    
    def switch_account(self) -> Dict[str, Any]:
        """切换到下一个账号"""
        self.current_account_index = (self.current_account_index + 1) % len(self.accounts)
        logger.info(f"切换到账号: {self.get_current_account()['phone']}")
        return self.get_current_account()
    
    def mark_account_failed(self):
        """标记当前账号失败"""
        self.accounts[self.current_account_index]['failure_count'] += 1
        
    def reset_account_failure(self):
        """重置当前账号失败计数"""
        self.accounts[self.current_account_index]['failure_count'] = 0


class TelegramScraper:
    """Telegram群组消息抓取器"""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.client = None
        self.max_failures = 10  # 连续失败5次切换账号
        
    async def create_client(self) -> TelegramClient:
        """创建Telegram客户端"""
        account = self.account_manager.get_current_account()
        
        # 代理配置
        proxy = None
        if account.get('proxy'):
            proxy_info = account['proxy']
            if len(proxy_info) >= 6:
                proxy = {
                    'proxy_type': 'socks5',
                    'addr': proxy_info[1],
                    'port': proxy_info[2],
                    'username': proxy_info[4],
                    'password': proxy_info[5]
                }
        print('proxy',proxy, account['session_file'])
        
        client = TelegramClient(
            account['session_file'],
            account['app_id'],
            account['app_hash'],
            proxy=proxy
        )
        
        try:
            await client.start(phone=account['phone'])
            if not await client.is_user_authorized():
                logger.error(f"账号 {account['phone']} 未授权")
                return None
            
            # 测试连接
            me = await client.get_me()
            logger.info(f"客户端创建成功: {me.phone}")
            logger.info(f"客户端创建成功: {account['phone']}")
            return client
        except Exception as e:
            logger.error(f"创建客户端失败 {account['phone']}: {e}")
            return None
    
    def get_entity_type(self, entity) -> str:
        """获取实体类型"""
        if isinstance(entity, Channel):
            return "channel"
        elif isinstance(entity, User):
            if entity.bot:
                return "bot"
            else:
                return "user"
        else:
            return "unknown"
    
    async def scrape_channel_messages(self, channel_name: str) -> Optional[Dict]:
        """抓取单个频道的消息"""
        if not self.client:
            return None
            
        try:
            # 获取频道实体
            try:
                entity = await self.client.get_entity(channel_name)
            except ValueError as e:
                if "No user has" in str(e) or "Could not find" in str(e):
                    logger.warning(f"频道不存在: {channel_name}")
                    # self.account_manager.mark_account_failed()
                    return None
                raise e
            
            # 重置失败计数
            self.account_manager.reset_account_failure()
            
            messages_data = []
            message_count = 0
            message_len = 0
            
            # 获取最近1000条消息
            async for message in self.client.iter_messages(entity, limit=MSG_LIMIT):
                if message_count >= MSG_LIMIT:
                    break
                    
                # 跳过服务消息
                if isinstance(message, MessageService):
                    continue
                
                sender_user = ""
                sender_username = ""
                entity_type = "unknown"
                
                if message.sender:
                    if hasattr(message.sender, 'first_name'):
                        sender_user = f"{message.sender.first_name or ''} {message.sender.last_name or ''}".strip()
                    if hasattr(message.sender, 'username') and message.sender.username:
                        sender_username = message.sender.username
                    entity_type = self.get_entity_type(message.sender)
                
                if sender_username == channel_name:
                    continue

                # 格式化时间
                time_str = message.date.strftime('%Y-%m-%d %H:%M:%S') if message.date else ""
                
                if message.text:
                    message_data = {
                        "message_id": message.id,
                        "sender_user": sender_user,
                        "sender_username": sender_username,
                        "message": message.text or "",
                        "time": time_str,
                        "entity_type": entity_type
                    }
                    
                    messages_data.append(message_data)
                    message_count += 1
                    message_len += len(message.text)
            
            if len(messages_data) <= 2:
                return
            
            # 构造返回数据
            result = {
                "channel": channel_name,
                "total_messages": len(messages_data),
                "fetch_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "messages": messages_data
            }
            
            logger.info(f"成功抓取频道 {channel_name}: {len(messages_data)} 条消息")
            with open("statistic_chn.txt", "a") as f:
                f.write(str(len(messages_data)) + '\n')
            
            with open("statistic_msg.txt", "a") as f:
                f.write(str(message_len) + '\n')

            return result
            
        except errors.ChannelPrivateError:
            logger.warning(f"频道 {channel_name} 是私有频道")
            self.account_manager.mark_account_failed()
            return None
        except errors.FloodWaitError as e:
            logger.warning(f"触发速率限制，等待 {e.seconds} 秒")
            # await asyncio.sleep(e.seconds)
            # return await self.scrape_channel_messages(channel_name)
            self.account_manager.mark_account_failed()
            return None
        except Exception as e:
            logger.error(f"抓取频道 {channel_name} 失败: {e}")
            logger.error(traceback.format_exc())
            return None
    
    async def process_channel(self, channel_name: str):
        """处理单个频道"""
        # 检查当前账号是否需要切换
        current_account = self.account_manager.get_current_account()
        if current_account['failure_count'] >= self.max_failures:
            logger.warning(f"账号 {current_account['phone']} 连续失败超过 {self.max_failures} 次，切换账号")
            self.account_manager.switch_account()
            
            # 重新创建客户端
            if self.client:
                await self.client.disconnect()
            self.client = await self.create_client()
            if not self.client:
                logger.error("无法创建新的客户端")
                return
        
        # 抓取消息
        data = await self.scrape_channel_messages(channel_name)
        if not data:
            return
        
        # 保存到本地文件
        os.makedirs('channel_messages', exist_ok=True)
        file_name = f"{channel_name.replace('@', '')}_messages.json"
        local_file = f"channel_messages/{file_name}"
        with open(local_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        upload_file(local_file, f"{prefix}/{file_name}")
        
        with open("scpraed.txt", "a") as f:
            f.writelines(channel_name+"\n")
        
        # 随机延迟，避免过于频繁的请求
        await asyncio.sleep(random.uniform(20, 25))


async def worker(scraper: TelegramScraper, channels: List[str]):
    """工作线程函数"""
    for channel in channels:
        await scraper.process_channel(channel)


async def main():
    """主函数"""
    try:
        # 初始化账号管理器
        account_manager = AccountManager()
        
        # 读取频道列表
        channels = []
        channels_file = Path('channels/channels.txt')
        if channels_file.exists():
            with open(channels_file, 'r', encoding='utf-8') as f:
                channels = [line.strip() for line in f if line.strip()]
        
        scraped = []
        scrapereds = Path('channels/channels_crawled.txt')
        if scrapereds.exists():
            with open(scrapereds, 'r', encoding='utf-8') as f:
                scraped = [line.strip() for line in f if line.strip()]
        
        if not channels:
            logger.error("未找到要抓取的频道列表")
            return
        
        channels = set(channels)
        scraped = set(scraped)
        logger.info(f"总共找到 {len(channels)} 个频道， 已经抓取{len(scraped)}")
        channels = list(set(channels) - set(scraped))
        logger.info(f" {len(channels)} 个频道需要抓取")
        
        # 创建抓取器实例
        scrapers = []
        max_threads = min(len(account_manager.accounts), 10)  # 最多10个线程
        
        for i in range(max_threads):
            scraper = TelegramScraper(account_manager)
            scraper.client = await scraper.create_client()
            if scraper.client:
                scrapers.append(scraper)
            
            # 切换到下一个账号
            if i < max_threads - 1:
                account_manager.switch_account()
        
        if not scrapers:
            logger.error("没有可用的客户端")
            return
        
        # 分配频道给不同的抓取器
        channels_per_scraper = len(channels) // len(scrapers)
        tasks = []
        
        for i, scraper in enumerate(scrapers):
            start_idx = i * channels_per_scraper
            if i == len(scrapers) - 1:
                # 最后一个抓取器处理剩余的所有频道
                end_idx = len(channels)
            else:
                end_idx = start_idx + channels_per_scraper
            
            assigned_channels = channels[start_idx:end_idx]
            if assigned_channels:
                task = asyncio.create_task(worker(scraper, assigned_channels))
                tasks.append(task)
                logger.info(f"分配 {len(assigned_channels)} 个频道给抓取器 {i+1}")
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        
        # 关闭所有客户端
        for scraper in scrapers:
            if scraper.client:
                await scraper.client.disconnect()
        
        logger.info("所有抓取任务完成")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())
