import asyncio
from telethon import TelegramClient
from telethon.tl.functions.contacts import SearchRequest
from telethon.tl.functions.channels import GetFullChannelRequest
from telethon.tl.types import InputPeerChannel
import os
from telethon.tl.functions.contacts import ResolveUsernameRequest
from telethon.errors import UsernameNotOccupiedError

API_HASH = "43682ab7d51a8cf2ee69550ad90052b2"
API_ID = "17872015"
phonenumber = "959681583277"

INPUT_FILES = ["keywords/chinese_topics.txt", "keywords/english_topics.txt"]
OUTPUT_FILE = "groups.txt"

def read_keywords_from_files(file_paths):
    """从多个文件中读取关键字，去重并返回列表"""
    keywords = set()
    for file_path in file_paths:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取非空行并去除前后空白
                for line in f:
                    line = line.strip()
                    if line:
                        keywords.add(line)
        except FileNotFoundError:
            print(f"警告：文件 {file_path} 未找到，已跳过")
        except Exception as e:
            print(f"读取文件 {file_path} 时出错：{e}")
    return list(keywords)

def append_to_groups_file(username):
    """将用户名追加写入输出文件，避免重复"""
    # 检查是否已存在该用户名
    existing = set()
    if os.path.exists(OUTPUT_FILE):
        with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
            existing = {line.strip() for line in f if line.strip()}
    
    if username and username not in existing:
        with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{username}\n")
        print(f"已添加新群组: @{username}")

async def search_groups_and_members(client, keyword):
    """搜索指定关键字的群组并返回符合条件的结果"""
    try:
        result = await client(SearchRequest(
            q=keyword,
            limit=20  # 最大返回数量，可根据需要调整
        ))
        
        output = []
        for chat in result.chats:
            # 只处理超级群组或广播频道
            if getattr(chat, 'megagroup', False) or getattr(chat, 'broadcast', False):
                name = chat.title or '(无标题)'
                try:
                    full = await client(GetFullChannelRequest(channel=chat))
                    count = full.full_chat.participants_count
                except Exception as e:
                    print(f"获取群组 {name} 成员数失败: {e}")
                    count = None
                
                output.append({
                    'title': name,
                    'id': chat.id,
                    'members_count': count,
                    'username': chat.username
                })
        return output
    except Exception as e:
        print(f"搜索关键字 '{keyword}' 时出错: {e}")
        return []

async def main():
    # 读取所有关键字
    keywords = read_keywords_from_files(INPUT_FILES)
    if not keywords:
        print("没有找到有效的关键字，程序退出")
        return
    
    print(f"共加载 {len(keywords)} 个关键字，开始搜索...")
    
    # 初始化客户端（一次初始化多次使用）
    client = TelegramClient(phonenumber, API_ID, API_HASH)
    
    try:
        # 逐个关键字搜索
        for keyword in keywords:
            print(f"\n正在搜索关键字: {keyword}")
            groups = await search_groups_and_members(client, keyword)
            
            # 处理搜索结果
            for group in groups:
                if group['members_count'] and group['members_count'] > 20:
                    append_to_groups_file(group['username'])
                # 打印详细信息（可选）
                print(f"{group['username'] or '无用户名'} {group['title']} (ID={group['id']}): 成员数 = {group['members_count']}")
    finally:
        await client.disconnect()
        print("\n客户端已断开连接，搜索完成")

if __name__ == '__main__':
    asyncio.run(main())