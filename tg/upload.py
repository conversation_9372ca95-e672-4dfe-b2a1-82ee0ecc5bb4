import os
import boto3
from tqdm import tqdm
from botocore.client import Config

# R2 连接配置
access_key = "6e17dce5e6699ec405f7ec07deecf321"
secret_key = "4916026eb627351979f06b898b4f8f67137caba46c39bab83934eb29a97f92ad"
endpoint_url = "https://6d18700b4bd3fff2b330035c35b0bbeb.r2.cloudflarestorage.com"

bucket_name = "tmp-transfer2"
prefix = 'telegram/0815'

# 本地目录 → R2 目标前缀配置（顺序对应）
local_input_dirs = [
    "/home/<USER>/tg/channel_messages/",
]

r2_target_prefixes = [
    "telegram/",
]

# 初始化 boto3 客户端
s3 = boto3.client(
    "s3",
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
    endpoint_url=endpoint_url,
    config=Config(signature_version="s3v4"),
    region_name="auto",
)

def check_r2_file_exists(bucket, key):
    """检查 R2 上是否存在某个文件"""
    try:
        s3.head_object(Bucket=bucket, Key=key)
        return True
    except:
        return False

def upload_file(local_path, r2_key):
    """上传单个文件"""
    try:
        s3.upload_file(local_path, bucket_name, r2_key)
        return True
    except Exception as e:
        print(f"❌ 上传失败: {r2_key} | 错误: {e}")
        return False

def upload_all_files():
    total_uploaded = 0
    total_files = 0

    # 预统计总任务数
    for local_dir in local_input_dirs:
        if os.path.exists(local_dir):
            total_files += len([f for f in os.listdir(local_dir) if f.endswith('.jsonl')])

    print(f"📦 共发现 {total_files} 个待上传的 .jsonl 文件")

    with tqdm(total=total_files, desc="上传进度") as pbar:
        for local_dir, r2_prefix in zip(local_input_dirs, r2_target_prefixes):
            if not os.path.exists(local_dir):
                print(f"⚠️ 本地目录不存在，跳过: {local_dir}")
                continue

            print(f"\n📁 正在处理目录: {local_dir} → {r2_prefix}")

            for filename in os.listdir(local_dir):
                if not filename.endswith('.jsonl'):
                    continue

                local_file = os.path.join(local_dir, filename)
                r2_key = r2_prefix + filename

                if check_r2_file_exists(bucket_name, r2_key):
                    print(f"⏭️ 已存在，跳过: {r2_key}")
                    pbar.update(1)
                    continue

                if upload_file(local_file, r2_key):
                    print(f"✅ 已上传: {r2_key}")
                    total_uploaded += 1

                pbar.update(1)

    print(f"\n🚀 上传完成：共上传 {total_uploaded} 个新文件")

# 列出指定前缀下的所有对象
def list_files_with_prefix(bucket, prefix):
    paginator = s3.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket, Prefix=prefix)

    files = []
    total = 0
    for page in pages:
        for obj in page.get("Contents", []):
            key = obj["Key"]
            # size = obj["Size"]
            # print(f"{key} ({size / 1024:.2f} KB)", key[len(prefix)+1:-14])
            files.append(key[len(prefix)+1:-14])
            total += 1
    print(f"\n📦 总文件数: {total}")
    return files, total

if __name__ == "__main__":
    f, t = list_files_with_prefix(bucket_name, prefix)
    print(f)
    import os

    dir_path = "./stable/channels_messages0815" 
    all_items = os.listdir(dir_path)
    file_names = [item[:-len("_messages.json")] for item in all_items if os.path.isfile(os.path.join(dir_path, item))]
    print(set(file_names) - set(f))
