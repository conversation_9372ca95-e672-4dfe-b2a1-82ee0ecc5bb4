#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Telegram频道消息抓取器启动脚本
使用示例: python run_scraper.py
"""

import os
import sys
from pathlib import Path

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv('.env')
except ImportError:
    print("提示: 安装 python-dotenv 以支持 .env 文件")

# 导入主程序
from telegram_channel_scraper import main
import asyncio

if __name__ == "__main__":
    # 检查必要的文件
    # if not Path('channels.txt').exists():
    #     print("错误: 未找到 channels.txt 文件")
    #     print("请在 channels.txt 文件中添加要抓取的频道名称，每行一个")
    #     sys.exit(1)
    
    # if not Path('account/tg_account').exists():
    #     print("错误: 未找到 account/tg_account 目录")
    #     print("请确保 Telegram 账号配置目录存在")
    #     sys.exit(1)
    
    print("开始启动 Telegram 频道消息抓取器...")
    print("按 Ctrl+C 停止程序")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)