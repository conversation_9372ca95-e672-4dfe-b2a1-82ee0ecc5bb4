#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 clean_russian.py 的俄文检测功能，遍历指定目录中的文件，
检查每行 JSON 中 message 字段的俄文占比，如果大于 90% 就删除文件。
"""

import os
import json
import regex
import argparse
from typing import Dict, List, Tuple
from pathlib import Path


class RussianTextDetector:
    """俄文文本检测器"""
    
    def __init__(self):
        # 使用 Unicode 属性转义匹配西里尔字母（俄文字符）
        self.cyrillic_pattern = regex.compile(r'[\p{Cyrillic}]')
        # 匹配所有字母字符（用于计算总字母数）
        self.letter_pattern = regex.compile(r'[\p{L}]')
    
    def get_russian_ratio(self, text: str) -> float:
        """
        计算文本中俄文字符的占比
        
        Args:
            text: 输入文本
            
        Returns:
            俄文字符占所有字母字符的比例 (0.0 - 1.0)
        """
        if not text or not isinstance(text, str):
            return 0.0
        
        # 查找所有字母字符
        all_letters = self.letter_pattern.findall(text)
        if not all_letters:
            return 0.0
        
        # 查找俄文字符
        russian_chars = self.cyrillic_pattern.findall(text)
        
        # 计算俄文占比
        russian_ratio = len(russian_chars) / len(all_letters)
        return russian_ratio
    
    def is_mostly_russian(self, text: str, threshold: float = 0.9) -> bool:
        """
        判断文本是否主要是俄文
        
        Args:
            text: 输入文本
            threshold: 俄文占比阈值，默认 0.9 (90%)
            
        Returns:
            如果俄文占比超过阈值返回 True
        """
        return self.get_russian_ratio(text) > threshold


class FileProcessor:
    """文件处理器"""
    
    def __init__(self, target_dir: str, threshold: float = 0.9, dry_run: bool = False):
        """
        初始化文件处理器
        
        Args:
            target_dir: 目标目录路径
            threshold: 俄文占比阈值
            dry_run: 是否为试运行模式（不实际删除文件）
        """
        self.target_dir = Path(target_dir)
        self.threshold = threshold
        self.dry_run = dry_run
        self.detector = RussianTextDetector()
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'deleted_files': 0,
            'error_files': 0,
            'skipped_files': 0
        }
    
    def check_file_russian_content(self, file_path: Path) -> Tuple[bool, Dict]:
        """
        检查文件中的俄文内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否应该删除, 详细信息字典)
        """
        info = {
            'total_lines': 0,
            'valid_json_lines': 0,
            'russian_lines': 0,
            'russian_ratio': 0.0,
            'should_delete': False,
            'error': None
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    info['total_lines'] += 1
                    
                    try:
                        # 解析 JSON
                        data = json.loads(line)
                        info['valid_json_lines'] += 1
                        
                        # 获取 message 字段
                        message = data.get('message', '')
                        if not message:
                            continue
                        
                        # 检查是否主要是俄文
                        if self.detector.is_mostly_russian(message, self.threshold):
                            info['russian_lines'] += 1
                    
                    except json.JSONDecodeError as e:
                        # JSON 解析错误，跳过这一行
                        continue
            
            # 计算俄文行的比例
            if info['valid_json_lines'] > 0:
                info['russian_ratio'] = info['russian_lines'] / info['valid_json_lines']
                # 如果俄文行占比超过阈值，标记为应删除
                info['should_delete'] = info['russian_ratio'] > self.threshold
            
        except Exception as e:
            info['error'] = str(e)
        
        return info['should_delete'], info
    
    def process_file(self, file_path: Path) -> bool:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否成功处理
        """
        try:
            should_delete, info = self.check_file_russian_content(file_path)
            
            if info['error']:
                print(f"ERROR 处理文件出错: {file_path}")
                print(f"   错误: {info['error']}")
                self.stats['error_files'] += 1
                return False

            print(f"FILE {file_path.name}")
            print(f"   总行数: {info['total_lines']}, 有效JSON行: {info['valid_json_lines']}")
            print(f"   俄文行数: {info['russian_lines']}, 俄文占比: {info['russian_ratio']:.2%}")

            if should_delete:
                if self.dry_run:
                    print(f"DRY-RUN [试运行] 将删除: {file_path}")
                else:
                    file_path.unlink()
                    print(f"DELETED 已删除: {file_path}")
                self.stats['deleted_files'] += 1
            else:
                print(f"KEEP 保留文件: {file_path}")

            self.stats['processed_files'] += 1
            return True

        except Exception as e:
            print(f"ERROR 处理文件失败: {file_path}")
            print(f"   错误: {e}")
            self.stats['error_files'] += 1
            return False
    
    def process_directory(self) -> None:
        """处理目录中的所有文件"""
        if not self.target_dir.exists():
            print(f"ERROR 目录不存在: {self.target_dir}")
            return

        if not self.target_dir.is_dir():
            print(f"ERROR 路径不是目录: {self.target_dir}")
            return

        # 获取所有文件
        all_files = []
        for file_path in self.target_dir.iterdir():
            if file_path.is_file():
                all_files.append(file_path)

        self.stats['total_files'] = len(all_files)

        if self.stats['total_files'] == 0:
            print(f"INFO 目录为空: {self.target_dir}")
            return

        print(f"INFO 开始处理目录: {self.target_dir}")
        print(f"INFO 发现 {self.stats['total_files']} 个文件")
        print(f"INFO 俄文占比阈值: {self.threshold:.1%}")
        if self.dry_run:
            print("INFO 试运行模式 - 不会实际删除文件")
        print("-" * 50)
        
        # 处理每个文件
        for file_path in all_files:
            self.process_file(file_path)
            print()
        
        # 输出统计信息
        self.print_summary()
    
    def print_summary(self) -> None:
        """打印处理结果摘要"""
        print("=" * 50)
        print("SUMMARY 处理结果摘要:")
        print(f"   总文件数: {self.stats['total_files']}")
        print(f"   已处理: {self.stats['processed_files']}")
        print(f"   已删除: {self.stats['deleted_files']}")
        print(f"   错误文件: {self.stats['error_files']}")
        print(f"   跳过文件: {self.stats['skipped_files']}")

        if self.stats['processed_files'] > 0:
            delete_ratio = self.stats['deleted_files'] / self.stats['processed_files']
            print(f"   删除比例: {delete_ratio:.1%}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="检查并删除包含大量俄文内容的文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python clean_russian_files.py telegram/0812
  python clean_russian_files.py telegram/0812 --threshold 0.8
  python clean_russian_files.py telegram/0812 --dry-run
        """
    )
    
    parser.add_argument(
        'directory',
        help='要处理的目录路径'
    )
    
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.9,
        help='俄文占比阈值 (0.0-1.0)，默认 0.9 (90%%)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式，不实际删除文件'
    )
    
    args = parser.parse_args()
    
    # 验证阈值
    if not 0.0 <= args.threshold <= 1.0:
        print("ERROR 阈值必须在 0.0 到 1.0 之间")
        return
    
    # 创建处理器并开始处理
    processor = FileProcessor(
        target_dir=args.directory,
        threshold=args.threshold,
        dry_run=args.dry_run
    )
    
    processor.process_directory()


if __name__ == "__main__":
    main()
