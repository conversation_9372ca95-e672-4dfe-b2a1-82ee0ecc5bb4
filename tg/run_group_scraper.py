#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Telegram群组抓取器启动脚本
"""

import asyncio
import sys
from pathlib import Path
from telegram_group_scraper import main

def check_requirements():
    """检查运行环境和必要文件"""
    print("检查运行环境...")
    
    # 检查账户目录
    account_dir = Path("account/tg_account_0807_2")
    if not account_dir.exists():
        print(f"账户目录不存在: {account_dir}")
        return False
    
    # 检查是否有账户配置
    account_count = 0
    for account_path in account_dir.iterdir():
        if account_path.is_dir():
            json_files = list(account_path.glob("*.json"))
            session_files = list(account_path.glob("*.session"))
            if json_files and session_files:
                account_count += 1
    
    if account_count == 0:
        print("未找到可用的账户配置")
        return False
    
    print(f"找到 {account_count} 个账户配置")
    
    # 检查频道文件
    channels_file = Path("channels/channels.txt")
    if not channels_file.exists():
        print(f"频道文件不存在: {channels_file}")
        print("请创建 channels/channels.txt 文件并添加要抓取的频道")
        return False
    
    # 读取频道数量
    with open(channels_file, 'r', encoding='utf-8') as f:
        channels = [line.strip() for line in f if line.strip()]
    
    if not channels:
        print("频道文件为空")
        return False
    
    print(f"找到 {len(channels)} 个频道")
    
    # 检查已完成的频道
    completed_file = Path("channels/channels_completed.txt")
    completed_count = 0
    if completed_file.exists():
        with open(completed_file, 'r', encoding='utf-8') as f:
            completed_channels = [line.strip() for line in f if line.strip()]
        completed_count = len(completed_channels)
    
    remaining_count = len(channels) - completed_count
    print(f"待处理频道: {remaining_count} 个")
    
    return True

def main_wrapper():
    """主函数包装器"""
    print("=" * 50)
    print("Telegram群组消息抓取器")
    print("=" * 50)
    
    if not check_requirements():
        print("\n环境检查失败，请修复上述问题后重试")
        sys.exit(1)
    
    print("\n环境检查通过，开始抓取...")
    print("=" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n程序结束")

if __name__ == "__main__":
    main_wrapper()
