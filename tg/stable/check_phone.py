import random
import telethon
from telethon import (
    TelegramClient,
    sync,
)  # need to keep sync in otherwise get await errors
from telethon.tl.types import (
    Channel, User, MessageService, Chat, 
    PeerChannel, PeerUser, PeerChat
)
import asyncio
import json
import os
from datetime import datetime
#from upload import upload_file, prefix

import sys
import io
import time
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

api_hash = "ed3e2291059b5e3c9d583a323956efc8"
api_id = 28917320
phonenumber = "+8617621390681"

api_hash = "43682ab7d51a8cf2ee69550ad90052b2"
api_id = "17872015"
phonenumber = "+8618013471137"

api_hash = "73940204450a7683bdcfb505037a5338"
api_id = 27053382
phonenumber = "+8618061991139"

api_hash = "84d9a7d82b4ff1cd095ff4bb21911cea"
api_id = 20044015
phonenumber = "+8618321260681"


# 0810
# api_hash = "1db71230ae5bf5bfe51c395776b66764"
# api_id = 25371857
# phonenumber = "+8618235663539"

# 0811
api_hash = "1e21318d4f26a7372c50bcf299942cc1"
api_id = 20374878
phonenumber = "+8617612129008"

api_hash = "1e4e0304e68225c293fee2ef9166703c"
api_id = 29076865
phonenumber = "+8615202186219"


# 0812
api_hash = "f19d4499d4e37453c85285004a4184c9"
api_id = 26802253
phonenumber = "+13642203680"

api_hash = "baa39eb94d51697f8e141336d699f912"
api_id = 27792665
phonenumber = "+13642464534"

api_hash = "571549810d04da0588ce2c85a21cb031"
api_id = 24905734
phonenumber = "+13802557302"

api_hash = "76e6ab014e96707ee7be73a306fcdf1a"
api_id = 25487400
phonenumber = "+15015365301"

api_hash = "d69abc76707eccddceeff8c41a320f63"
api_id = 26710839
phonenumber = "+15015731228"


api_hash = "b5aedd3c80fdf32b62a64f9034b3966c"
api_id = 22082229
phonenumber = "+15303776201"

api_hash = "ccc01af960b41a3eeceef30510780c87"
api_id = 27354904
phonenumber = "+15303779323"

api_hash = "c5303cb34816eee3abcf9a882a40448d"
api_id = 27754025
phonenumber = "+15303779328"

api_hash = "7234238749101ca06447b13039f53a8f"
api_id = 26133265
phonenumber = "+15303770914"

api_hash = "f0104dfa8906b42a40fa9bc397ead022"
api_id = 25877581
phonenumber = "+15303770917"


api_hash = "415b5fb924f4e27066308c129dff1a8b"
api_id = 26725296
phonenumber = "+15303770952"

api_hash = "2ed9ee1458de3806ec754334455f49ef"
api_id = 22061712
phonenumber = "+15303770954"

api_hash = "31345b817cf342245a50832d04afd213"
api_id = 16123336
phonenumber = "+15303770960"



api_hash = "1ed49aded0d3df08beeb7c753c9e5a10"
api_id = 24944930
phonenumber = "+13802720217"

# 0810
api_hash = "1db71230ae5bf5bfe51c395776b66764"
api_id = 25371857
phonenumber = "+8618235663539"

proxy = {
    'proxy_type': 'http',
    'addr': "6zu4wmwq3hepp-as.ipidea.online",
    'port': 2333,
    'username': "furion2025-zone-ytb2319538",
    'password': "furion2025"
}

proxy = None

def create_client():
    client = TelegramClient(phonenumber, api_id, api_hash, proxy=proxy)
    client.start(phone=phonenumber)
    if not client.is_user_authorized():
        print(f"账号 {phonenumber} 未授权")
        return None
    return client

async def create_client():
    """创建Telegram客户端"""
    try:
        client = TelegramClient(
            phonenumber,
            api_id,
            api_hash,
            proxy=proxy
        )
        await client.start(phone=phonenumber)
        print(f"客户端连接成功 {phonenumber}")
        return client
    except Exception as e:
        print(f"创建客户端失败 {e}")
        return None
        
if __name__ == "__main__":
    asyncio.run(create_client())
