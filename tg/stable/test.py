import random
import telethon
from telethon import (
    TelegramClient,
    sync,
)  # need to keep sync in otherwise get await errors
from telethon.tl.types import (
    Channel, User, MessageService, Chat, 
    PeerChannel, PeerUser, PeerChat
)
import asyncio
import json
import os
from datetime import datetime
from upload import upload_file, prefix

import sys
import io
import time
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')


api_hash = "43682ab7d51a8cf2ee69550ad90052b2"
api_id = "17872015"
phonenumber = "+8618013471137"


with open("../channels/channels.txt", 'r', encoding='utf-8') as f:
    channels = [line.strip() for line in f if line.strip()]
        
with open("../channels/channels_crawled.txt", 'r', encoding='utf-8') as f:
    completed_channels = [line.strip() for line in f if line.strip()]
    
will_crawler_channels = list(set(channels) - set(completed_channels))
        
print(f"加载了 {len(will_crawler_channels)} 个待处理频道")


proxy = {
    'proxy_type': 'http',
    'addr': "6zu4wmwq3hepp-as.ipidea.online",
    'port': 2333,
    'username': "furion2025-zone-ytb2319538",
    'password': "furion2025"
}

def create_client():
    client = TelegramClient(phonenumber, api_id, api_hash, proxy=proxy)
    client.start(phone=phonenumber)
    if not client.is_user_authorized():
        print(f"账号 {phonenumber} 未授权")
        return None
    return client

def get_entity_type(entity) -> str:
    """获取实体类型"""
    if isinstance(entity, Channel):
        return "channel"
    elif isinstance(entity, Chat):
        return "group"
    elif isinstance(entity, User):
        return "bot" if entity.bot else "user"
    else:
        return "unknown"
        
def parse_messages(client, channel_name):
    try:
        # 获取频道实体
        try:
            entity = client.get_entity(channel_name)
        except ValueError as e:
            if "No user has" in str(e) or "Could not find" in str(e):
                print(f"频道不存在: {channel_name}")
                return None
            raise e
        
        messages_data = []
        message_count = 0
        message_len = 0

        # 获取最近3500条消息
        for message in client.iter_messages(entity, limit=3500):
            if message_count >= 3500:
                break
                
            # 跳过服务消息
            if isinstance(message, MessageService):
                continue
            
            # 只处理有文本的消息
            if not message.text:
                continue
            
            sender_user = ""
            sender_username = ""
            entity_type = "user"
            
            if message.sender:
                if hasattr(message.sender, 'first_name'):
                    sender_user = f"{message.sender.first_name or ''} {message.sender.last_name or ''}".strip()
                if hasattr(message.sender, 'username') and message.sender.username:
                    sender_username = message.sender.username
                
                entity_type = get_entity_type(message.sender)
            
            # 格式化时间
            time_str = message.date.strftime('%Y-%m-%d %H:%M:%S') if message.date else ""
            
            message_data = {
                "message_id": message.id,
                "sender_user": sender_user,
                "sender_username": sender_username,
                "message": message.text,
                "time": time_str,
                "entity_type": entity_type
            }
            
            messages_data.append(message_data)
            message_count += 1
            message_len += len(message.text)
        
        # 构造返回数据
        result = {
            "channel": channel_name,
            "total_messages": len(messages_data),
            "fetch_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "messages": messages_data
        }
        
        # 保存到文件
        os.makedirs('channel_messages', exist_ok=True)
        file_name = f"{channel_name.replace('@', '')}_messages.json"
        file_path = f"channel_messages/{file_name}"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        upload_file(file_path, f"{prefix}/{file_name}")
        print(f"成功抓取频道 {channel_name}: {len(messages_data)} 条消息")
        
        # 记录到已完成列表
        with open("../channels/channels_completed.txt", "a", encoding='utf-8') as f:
            f.write(channel_name + "\n")
        
        with open("statistic.txt", "a", encoding='utf-8') as f:
            f.write(f"{channel_name},{message_count},{message_len}\n")
        
        return result
        
    except Exception as e:
        print(f"抓取频道 {channel_name} 失败: {e}")
        return None

def main():
    client = create_client()
    if client is None:
        print('create error')
        return
    
    for channel_name in will_crawler_channels:
        parse_messages(client, channel_name)
        time.sleep(random.uniform(10, 20))

if __name__ == "__main__":
    main()
