#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Telegram频道历史消息分页抓取器 - 多线程版本
使用iter_messages方法分页抓取全部数据，支持多账号并发
"""

import asyncio
import json
import logging
import os
import random
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, List
from dataclasses import dataclass
from threading import Lock
from telethon import TelegramClient, errors
from telethon.tl.types import Channel, Chat, User, MessageService
from tg_account import TG_ACCOUNTS
from compile_count import count_en_words, count_zh_chars

# 配置参数
THREAD_COUNT = 18 # 线程数量
MAX_RETRIES_PER_CHANNEL = 2  # 每个频道最大重试次数
PAGE_SIZE = 1000

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/deepseek-crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ChannelStatus:
    """频道状态跟踪"""
    name: str
    completed: bool = False
    retry_count: int = 0
    in_progress: bool = False

class AccountPool:
    """账户池管理器"""
    
    def __init__(self):
        self.accounts = TG_ACCOUNTS.copy()
        self.account_lock = Lock()
    
    def get_available_account(self):
        """获取可用账户"""
        with self.account_lock:
            for account in self.accounts:
                if not account['in_use'] and account['failure_count'] < 5:
                    account['in_use'] = True
                    return account
            return None
    
    def release_account(self, account_id: str):
        """释放账户"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['in_use'] = False
                    break
    
    def mark_account_failed(self, account_id: str):
        """标记账户失败"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['failure_count'] += 1
                    account['in_use'] = False
                    break

class ChannelPool:
    """频道池管理器"""
    
    def __init__(self, channels_file: str = 'channels.txt'):
        self.channels_status = {}
        self.channel_lock = Lock()
        self.load_channels(channels_file)
    
    def load_channels(self, channels_file: str):
        """加载频道列表"""
        try:
            with open(channels_file, 'r', encoding='utf-8') as f:
                channels = [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            logger.error(f"频道文件 {channels_file} 不存在")
            channels = []
        
        # 加载已完成的频道
        completed_channels = self.load_completed_channels()

        # 加载手动剔除的频道
        ignore_channels = self.load_ignore_channels()
        
        # 初始化频道状态
        for channel in channels:
            if channel not in completed_channels and channel not in ignore_channels:
                self.channels_status[channel] = ChannelStatus(name=channel)
        
        logger.info(f"加载了 {len(self.channels_status)} 个待处理频道")
    
    def load_completed_channels(self) -> set:
        """加载已完成的频道"""
        try:
            with open("channels_completed.txt", 'r', encoding='utf-8') as f:
                return set(line.strip() for line in f if line.strip())
        except FileNotFoundError:
            return set()
    
    def load_ignore_channels(self) -> set:
        """加载手动剔除的频道"""
        try:
            with open("channels_ignore.txt", 'r', encoding='utf-8') as f:
                return set(line.strip() for line in f if line.strip())
        except FileNotFoundError:
            return set()
    
    def get_next_channel(self) -> Optional[ChannelStatus]:
        """获取下一个需要处理的频道"""
        with self.channel_lock:
            for channel_status in self.channels_status.values():
                if (not channel_status.completed and
                    channel_status.retry_count < MAX_RETRIES_PER_CHANNEL and
                    not channel_status.in_progress):
                    channel_status.in_progress = True
                    return channel_status
            return None
    
    def mark_channel_completed(self, channel_name: str):
        """标记频道完成"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].completed = True
                self.channels_status[channel_name].in_progress = False
    
    def release_channel(self, channel_name: str):
        """释放频道（失败时调用）"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].in_progress = False
                self.channels_status[channel_name].retry_count += 1

class TelegramCrawler:
    def __init__(self, account_pool: AccountPool, channel_pool: ChannelPool, worker_id: int):
        self.account_pool = account_pool
        self.channel_pool = channel_pool
        self.worker_id = worker_id
        self.client = None
        self.current_account = None
        
    async def create_client(self, account):
        """创建Telegram客户端"""
        try:
            client = TelegramClient(
                account['session_file'],
                account['api_id'],
                account['api_hash'],
                proxy=account['proxy']
            )
            await client.start(phone=account['phone'])
            logger.info(f"Worker {self.worker_id}: 客户端连接成功 {account['phone']}")
            return client
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 创建客户端失败 {e}")
            return None
    
    def get_entity_type(self, entity):
        """获取实体类型"""
        if isinstance(entity, User):
            return "user"
        elif isinstance(entity, Channel):
            return "channel"
        elif isinstance(entity, Chat):
            return "chat"
        return "unknown"
    
    async def scrape_channel_all_messages(self, channel_name: str) -> bool:
        """使用iter_messages抓取频道全部消息"""
        try:
            entity = await self.client.get_entity(channel_name)

            # 在方法开始时获取日期，确保整个抓取过程使用同一个日期
            today = datetime.now().strftime("%m%d")  # 格式：0817, 0818
            date_dir = f"channels_messages/{today}"
            os.makedirs(date_dir, exist_ok=True)

            file_name = f"{channel_name.replace('@', '').replace('/', '_')}_messages.json"
            file_path = f"{date_dir}/{file_name}"
            
            message_count = 0
            total_characters_en = 0
            total_characters_zh = 0
            logger.info(f"Worker {self.worker_id}: 开始抓取频道 {channel_name}")
            

            latest_msg = await self.client.get_messages(channel_name, limit=1)
            if not latest_msg:
                logger.info(f"{channel_name} 频道没有消息")
                return
            current_id = latest_msg[0].id

            while current_id > 0:
                # 从 current_id 往前取 page_size 条
                msgs = await self.client.get_messages(channel_name, limit=PAGE_SIZE, max_id=current_id)
                if not msgs:
                    break

                for message in msgs:
                    if isinstance(message, MessageService) or not message.text:
                        continue
                    
                    sender_user = ""
                    sender_username = ""
                    entity_type = "unknown"
                    
                    if message.sender:
                        if hasattr(message.sender, 'first_name'):
                            sender_user = f"{message.sender.first_name or ''} {message.sender.last_name or ''}".strip()
                        if hasattr(message.sender, 'username') and message.sender.username:
                            sender_username = message.sender.username
                        entity_type = self.get_entity_type(message.sender)
                    
                    if sender_username == channel_name.replace('@', ''):
                        continue
                    
                    time_str = message.date.strftime('%Y-%m-%d %H:%M:%S') if message.date else ""
                    
                    message_data = {
                        "message_id": message.id,
                        "sender_user": sender_user,
                        "sender_username": sender_username,
                        "message": message.text,
                        "time": time_str,
                        "entity_type": entity_type,
                        "channel": channel_name
                    }
                    
                    # 按行追加写入JSON
                    with open(file_path, 'a', encoding='utf-8') as f:
                        json.dump(message_data, f, ensure_ascii=False)
                        f.write(',\n')
                    
                    message_count += 1
                    total_characters_en += count_en_words(message.text)
                    total_characters_zh += count_zh_chars(message.text)
                    
                    if message_count % 1000 == 0:
                        logger.info(f"Worker {self.worker_id}: 频道 {channel_name} 已处理 {message_count} 条消息")
                    
                current_id = msgs[-1].id 
                await asyncio.sleep(random.uniform(15, 25))
                

            logger.info(f"Worker {self.worker_id}: 完成抓取频道 {channel_name}: {message_count} 条消息")
            
            # 记录到完成文件
            with open("channels_completed.txt", "a", encoding='utf-8') as f:
                f.write(f"{channel_name}\n")
            
            # 记录统计信息 - 使用方法开始时的日期，确保一致性
            os.makedirs('statistics', exist_ok=True)
            statistics_file = f"statistics/statistics{today}.txt"
            with open(statistics_file, "a", encoding='utf-8') as f:
                f.write(f"{channel_name},{message_count},{total_characters_zh},{total_characters_en},{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            return True
            
        except errors.ChannelPrivateError:
            logger.warning(f"Worker {self.worker_id}: 频道 {channel_name} 是私有频道")
            return False
        except errors.FloodWaitError as e:
            logger.warning(f"Worker {self.worker_id}: 触发速率限制，等待 {e.seconds} 秒")
            await asyncio.sleep(e.seconds)
            return False
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 抓取频道 {channel_name} 失败: {e}")
            return False
    
    async def process_channel(self, channel_status: ChannelStatus):
        """处理单个频道"""
        channel_name = channel_status.name
        
        # 获取可用账户
        account = self.account_pool.get_available_account()
        if not account:
            logger.warning(f"Worker {self.worker_id}: 没有可用账户处理频道 {channel_name}")
            return False
        
        try:
            self.current_account = account

            # 创建客户端
            self.client = await self.create_client(account)
            if not self.client:
                # 创建客户端失败，标记账户失败
                self.account_pool.mark_account_failed(account['id'])
                logger.warning(f"Worker {self.worker_id}: 账户 {account['phone']} 创建客户端失败，已从账户池移除")
                return False
            
            # 抓取消息
            success = await self.scrape_channel_all_messages(channel_name)
            
            if success:
                self.channel_pool.mark_channel_completed(channel_name)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 处理频道 {channel_name} 时出错: {e}")
            self.account_pool.mark_account_failed(account['id'])
            return False
        finally:
            if self.client:
                await self.client.disconnect()
            self.account_pool.release_account(account['id'])

async def worker_thread(account_pool: AccountPool, channel_pool: ChannelPool, worker_id: int):
    """工作线程函数"""
    crawler = TelegramCrawler(account_pool, channel_pool, worker_id)
    
    try:
        while True:
            # 获取下一个需要处理的频道
            channel_status = channel_pool.get_next_channel()
            if not channel_status:
                logger.info(f"Worker {worker_id}: 没有更多频道需要处理")
                break
            
            logger.info(f"Worker {worker_id}: 开始处理频道 {channel_status.name}")
            
            try:
                success = await crawler.process_channel(channel_status)
                
                if not success:
                    logger.warning(f"Worker {worker_id}: 频道 {channel_status.name} 处理失败")
                    channel_pool.release_channel(channel_status.name)
            except Exception as e:
                logger.error(f"Worker {worker_id}: 处理频道 {channel_status.name} 时出错: {e}")
                channel_pool.release_channel(channel_status.name)
            
            # 随机延迟
            await asyncio.sleep(random.uniform(5, 10))
            
    except Exception as e:
        logger.error(f"Worker {worker_id}: 工作线程出错: {e}")

async def main():
    try:
        logger.info("启动Telegram频道消息抓取器 - 多线程版本")
        
        # 创建必要的目录 - 根据日期创建子目录
        today = datetime.now().strftime("%m%d")  # 格式：0817, 0818
        date_dir = f"channels_messages/{today}"
        os.makedirs(date_dir, exist_ok=True)
        
        # 初始化账户池和频道池
        account_pool = AccountPool()
        channel_pool = ChannelPool()
        
        if not account_pool.accounts:
            logger.error("没有可用的账号")
            return
        
        if not channel_pool.channels_status:
            logger.error("没有需要处理的频道")
            return
        
        logger.info(f"账户池: {len(account_pool.accounts)} 个账号")
        logger.info(f"频道池: {len(channel_pool.channels_status)} 个频道")
        
        # 创建工作线程
        thread_count = min(THREAD_COUNT, len(account_pool.accounts))
        tasks = []
        
        for i in range(thread_count):
            task = asyncio.create_task(worker_thread(account_pool, channel_pool, i + 1))
            tasks.append(task)
            logger.info(f"启动工作线程 {i + 1}")
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        
        logger.info("所有抓取任务完成")
        
        # 输出统计信息
        completed_count = sum(1 for status in channel_pool.channels_status.values() if status.completed)
        total_count = len(channel_pool.channels_status)
        logger.info(f"处理完成: {completed_count}/{total_count} 个频道")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(main())
