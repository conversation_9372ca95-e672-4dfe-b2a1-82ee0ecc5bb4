#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 中文字符数（把常见中文标点也算上）
_ZH_RE = re.compile(
    r"["                      # 汉字
    r"\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF"
    r"\U00020000-\U0002A6DF\U0002A700-\U0002B73F"
    r"\U0002B740-\U0002B81F\U0002B820-\U0002CEAF"
    r"\U0002CEB0-\U0002EBEF"
    r"\u3000-\u303F"          # CJK 符号和标点
    r"\uFE30-\uFE4F"          # CJK 兼容形式（标点）
    r"\uFF01-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF40\uFF5B-\uFF65"  # 全角标点
    r"\u2018\u2019\u201C\u201D\u2013\u2014\u2026"            # ‘ ’ “ ” – — …
    r"]"
)

def count_zh_chars(s: str) -> int:
    return len(_ZH_RE.findall(s))


# 英文单词数（尽量多匹配：含数字、缩写、连字符/撇号/斜杠/点，至少含1个字母）
_EN_RE = re.compile(
    r"(?i)(?<![A-Za-z0-9])"                       # 左边非字母数字
    r"(?=[A-Za-z0-9]*[A-Za-z])"                   # 至少包含一个字母
    r"[A-Za-z0-9]+(?:[.'’\-\u2011\u2013\u2014/][A-Za-z0-9]+)*"  # don't, U.S.A., state-of-the-art, GPU-3D/AI
    r"(?![A-Za-z0-9])"                            # 右边非字母数字
)

def count_en_words(s: str) -> int:
    return len(_EN_RE.findall(s))
