import os
import boto3
from tqdm import tqdm
from botocore.client import Config

# R2 连接配置
access_key = "6e17dce5e6699ec405f7ec07deecf321"
secret_key = "4916026eb627351979f06b898b4f8f67137caba46c39bab83934eb29a97f92ad"
endpoint_url = "https://6d18700b4bd3fff2b330035c35b0bbeb.r2.cloudflarestorage.com"

bucket_name = "tmp-transfer2"
prefix = 'telegram/0821'

# 本地目录 → R2 目标前缀配置（顺序对应）
local_input_dirs = "./channels_messages821/"

# 初始化 boto3 客户端
s3 = boto3.client(
    "s3",
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
    endpoint_url=endpoint_url,
    config=Config(signature_version="s3v4"),
    region_name="auto",
)

def upload_file(local_path, r2_key):
    """上传单个文件"""
    try:
        s3.upload_file(local_path, bucket_name, r2_key)
        return True
    except Exception as e:
        print(f"❌ 上传失败: {r2_key} | 错误: {e}")
        return False


if __name__ == "__main__":
    for root, dirs, files in os.walk(local_input_dirs):
        for file in files:
            # 构建本地文件完整路径
            local_file_path = os.path.join(root, file)
            print(local_file_path)
            upload_file(local_file_path,  f"{prefix}/{file}")