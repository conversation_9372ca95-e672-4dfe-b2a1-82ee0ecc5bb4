import os
import boto3
from tqdm import tqdm
from botocore.client import Config
import json

import re

# 中文字符数（把常见中文标点也算上）
_ZH_RE = re.compile(
    r"["                      # 汉字
    r"\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF"
    r"\U00020000-\U0002A6DF\U0002A700-\U0002B73F"
    r"\U0002B740-\U0002B81F\U0002B820-\U0002CEAF"
    r"\U0002CEB0-\U0002EBEF"
    r"\u3000-\u303F"          # CJK 符号和标点
    r"\uFE30-\uFE4F"          # CJK 兼容形式（标点）
    r"\uFF01-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF40\uFF5B-\uFF65"  # 全角标点
    r"\u2018\u2019\u201C\u201D\u2013\u2014\u2026"            # ‘ ’ “ ” – — …
    r"]"
)

def count_zh_chars(s: str) -> int:
    return len(_ZH_RE.findall(s))


# 英文单词数（尽量多匹配：含数字、缩写、连字符/撇号/斜杠/点，至少含1个字母）
_EN_RE = re.compile(
    r"(?i)(?<![A-Za-z0-9])"                       # 左边非字母数字
    r"(?=[A-Za-z0-9]*[A-Za-z])"                   # 至少包含一个字母
    r"[A-Za-z0-9]+(?:[.'’\-\u2011\u2013\u2014/][A-Za-z0-9]+)*"  # don't, U.S.A., state-of-the-art, GPU-3D/AI
    r"(?![A-Za-z0-9])"                            # 右边非字母数字
)

def count_en_words(s: str) -> int:
    return len(_EN_RE.findall(s))


# R2 连接配置
access_key = "6e17dce5e6699ec405f7ec07deecf321"
secret_key = "4916026eb627351979f06b898b4f8f67137caba46c39bab83934eb29a97f92ad"
endpoint_url = "https://6d18700b4bd3fff2b330035c35b0bbeb.r2.cloudflarestorage.com"

bucket_name = "tmp-transfer2"
prefix = 'telegram/'

# 初始化 boto3 客户端
s3 = boto3.client(
    "s3",
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
    endpoint_url=endpoint_url,
    config=Config(signature_version="s3v4"),
    region_name="auto",
)

def list_files_with_prefix(bucket, prefix):
    paginator = s3.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket, Prefix=prefix)

    files = []
    for page in pages:
        for obj in page.get("Contents", []):
            key = obj["Key"]
            files.append(key)

    return files

def read_file_content(bucket, key):
    """读取指定文件的内容并返回"""
    try:
        # 获取文件对象
        response = s3.get_object(Bucket=bucket, Key=key)
        # 读取内容（根据文件类型选择适当的编码方式）
        content = response['Body'].read().decode('utf-8')  # 文本文件使用utf-8解码
        return content
    except Exception as e:
        print(f"读取文件 {key} 时出错: {str(e)}")
        return None

def read_file_line_by_line(bucket, key):
    """生成器函数，逐行读取文件内容"""
    try:
        response = s3.get_object(Bucket=bucket, Key=key)
        # 按行读取大文件时推荐使用这种方式
        for line in response['Body'].iter_lines():
            if line:  # 跳过空行
                yield line.decode('utf-8')
    except Exception as e:
        print(f"读取文件 {key} 时出错: {str(e)}")
        return

if __name__ == "__main__":
    for p in ['0807','0808','0809', '0810', '0811']:
        en_charts = 0
        zh_charts = 0
        chn = 0
        msg = 0
        for i in list_files_with_prefix(bucket_name, prefix+p):
            chn += 1
            data = read_file_content(bucket_name, i)
            for message in json.loads(data)['messages']:
                msg += 1
                en_charts += count_en_words(message["message"])
                zh_charts += count_zh_chars(message["message"])
            
            print(i, msg, en_charts, zh_charts)
        
        with open("all.txt", "a") as f:
            f.write(f"{p} 频道数：{chn} 消息数：{msg} 中文：{zh_charts} 英文：{en_charts}\n")
    
    for p in ['0812','0813','0814']:
        en_charts = 0
        zh_charts = 0
        chn = 0
        msg = 0
        for i in list_files_with_prefix(bucket_name, prefix+p):
            chn += 1
            line_generator = read_file_line_by_line(bucket_name, i)
            if line_generator:
                for data, line in enumerate(line_generator):
                    message = json.loads(line[:-1])["message"]
                    msg += 1
                    en_charts += count_en_words(message)
                    zh_charts += count_zh_chars(message)
                print(i, msg, en_charts, zh_charts)
        
        with open("all.txt", "a") as f:
            f.write(f"{p} 频道数：{chn} 消息数：{msg} 中文：{zh_charts} 英文：{en_charts}\n")


'''


'''