date = '0821'
with open(f"statistics{date}.txt", "r") as f:
    data = f.readlines()

msg = 0
total_characters_zh = 0
total_characters_en = 0
chn = 0
for d in data:
    a = d.split(",")
    msg += int(a[1])
    total_characters_zh += int(a[2])
    total_characters_en += int(a[3])
    chn += 1

print(f"{date} 频道数:{chn} 消息数:{msg}, 中文：{total_characters_zh} 英文：{total_characters_en}")

import requests
import json
from datetime import datetime

"""
发送统计数据到飞书Webhook

参数:
    webhook_url: 飞书机器人的Webhook地址
    channel_count: 频道数量
    total_messages: 总消息数
    chinese_messages: 中文消息数
    english_messages: 英文消息数
"""
# 当前日期时间
current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 构建飞书消息卡片
payload = {
  "msg_type": "interactive",
  "card": {
    "schema": "2.0",
    "config": {
      "update_multi": True,
      "style": {
        "text_size": {
          "normal_v2": {
            "default": "normal"
          }
        }
      }
    },
    "header": {
      "title": {
        "tag": "plain_text",
        "content": f"{date} Telegram消息数据统计"
      },
      "template": "blue",
      "padding": "12px 12px 12px 12px"
    },
    "body": {
      "direction": "vertical",
      "padding": "16px 12px 16px 12px",
      "elements": [
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": "### 当日核心数据汇总"
          },
          "margin": "0px 0px 12px 0px"
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": f"- **频道总数**： {chn} 个"
          },
          "margin": "0px 0px 8px 0px"
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": f"- **消息总量**：{msg} 条"
          },
          "margin": "0px 0px 8px 0px"
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": f"- **中文**：{total_characters_zh} "
          },
          "margin": "0px 0px 8px 0px"
        },
        {
          "tag": "div",
          "text": {
            "tag": "lark_md",
            "content": f"- **英文**：{total_characters_en} "
          },
          "margin": "0px 0px 16px 0px"
        }
      ]
    }
  }
}

try:
    # 发送POST请求
    response = requests.post(
        'https://open.feishu.cn/open-apis/bot/v2/hook/3fd52520-7ce9-46f4-9491-b12dbc61c30d',
        headers={"Content-Type": "application/json"},
        data=json.dumps(payload)
    )
    
    # 检查响应状态
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            print("消息发送成功")
        else:
            print(f"消息发送失败: {result.get('msg')}")
    else:
        print(f"请求失败，状态码: {response.status_code}")
        
except Exception as e:
    print(f"发送过程中发生错误: {str(e)}")
    

'''
目标3000亿条数据
0807 频道数：2949 消息数： 2564044 中文： 22113646 英文： 53725801
0808 频道数：307 消息数： 824866 中文： 16047549 英文： 13629744
0809 频道数：97 消息数： 262731 中文： 5858541 英文： 4061339
0810 频道数：563 消息数： 1483586 中文： 51698242 英文： 22874842
0811 频道数：666 消息数： 1779271 中文： 55665962 英文： 27107841
0812 频道数：26 消息数： 5230928 中文： 120870859 英文： 54482132
0813 频道数：152 消息数： 13951620 中文： 55457093 英文： 283561799
0814 频道数：111 消息数： 15987080 中文： 97710647 英文： 156222639
0815 频道数：211 消息数： 31041830 中文： 182228373 英文： 582277565
0807-0815 总计 中文： 6.07亿  英文：11.97亿    消息数： 73125956  中文  607650912 英文 1197943702

0816 频道数: 255 消息数: 34672570, 中文： 171430172 英文： 470294211
0817 频道数: 237 消息数: 31767872, 中文： 185841112 英文： 465479519
0818 频道数: 237 消息数: 32781649, 中文： 142322229 英文： 332398943
0819 频道数: 356 消息数: 45209951, 中文： 297704434 英文： 519516806
0820 频道数: 344 消息数: 47377211, 中文： 251393085 英文： 523984207
0821 频道数: 324 消息数: 45901663, 中文： 297705430 英文： 525516672
'''