
# ignore_channels = []
# with open("tmp.txt", "r") as f:
#     for line in f:
#         line = line.strip()
#         ignore_channels.append(line[len("2025-08-11 09:54:16,242 - telegram_group_scraper - WARNING - Worker 4: 尝试了 4 个账号都无法找到频道 "):])

# print(ignore_channels)
# with open("channels/channels_ignore.txt", "w") as f:
#     for i in list(set(ignore_channels)):
#         f.write(i+"\n")
chn = 0
msg = 0
with open('group_statistics.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        msg += int(line.split(",")[1])
        chn += 1



import os

def get_all_files(folder_path):
    """
    递归获取文件夹下所有文件的路径
    
    参数:
        folder_path: 文件夹路径
        
    返回:
        包含所有文件路径的列表
    """
    all_files = []
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 '{folder_path}' 不存在")
        return all_files
        
    # 检查是否是文件夹
    if not os.path.isdir(folder_path):
        print(f"错误: '{folder_path}' 不是一个文件夹")
        return all_files
    
    # 遍历文件夹
    for entry in os.scandir(folder_path):
        if entry.is_file():
            # 如果是文件，添加到列表
            all_files.append(entry.path)
        elif entry.is_dir():
            # 如果是文件夹，递归处理
            subfolder_files = get_all_files(entry.path)
            all_files.extend(subfolder_files)
            
    return all_files

import json

sss = 0
for i in get_all_files("./channel_messages"):
    with open(i, "r") as f:
        data = json.loads(f.read())
        for d in data["messages"]:
            sss += len(str(d['message']))
        if data['total_messages'] >= 3300:
            print(data['channel'])



print(f"总频道：{chn} 消息数：{msg} 字符数：{sss}")