# 首先安装模块：pip install regex
import regex

# 使用Unicode属性转义
pattern = r'[\p{Cyrillic}]'

# 测试文本
text = "Привет мир! Hello world! 你好，世界！"

# 查找所有俄文字符
russian_chars = regex.findall(pattern, text)
print("所有俄文字符:", ''.join(russian_chars))

# 匹配整个俄文单词
word_pattern = r'\b[\p{Cyrillic}]+\b'
russian_words = regex.findall(word_pattern, text)
print("所有俄文单词:", russian_words)

# 判断文本是否包含俄文
has_russian = bool(regex.search(pattern, text))
print("是否包含俄文:", has_russian)