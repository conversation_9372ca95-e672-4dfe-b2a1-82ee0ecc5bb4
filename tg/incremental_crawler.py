#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Telegram频道增量消息抓取器
从S3读取已有的channel_message.json文件，获取最新消息ID，然后增量抓取到最新数据
"""

import asyncio
import json
import logging
import os
import random
import traceback
import glob
from datetime import datetime
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from threading import Lock
import boto3
from botocore.client import Config
from telethon import TelegramClient, errors
from telethon.tl.types import Channel, Chat, User, MessageService
from stable.compile_count import count_en_words, count_zh_chars

# 配置参数
THREAD_COUNT = 5  # 线程数量
MAX_RETRIES_PER_CHANNEL = 2  # 每个频道最大重试次数
PAGE_SIZE = 1000
MAX_CHANNELS_TO_PROCESS = 500  # 限制处理的频道数量，避免一次处理太多

# S3/R2 连接配置
S3_ACCESS_KEY = "6e17dce5e6699ec405f7ec07deecf321"
S3_SECRET_KEY = "4916026eb627351979f06b898b4f8f67137caba46c39bab83934eb29a97f92ad"
S3_ENDPOINT_URL = "https://6d18700b4bd3fff2b330035c35b0bbeb.r2.cloudflarestorage.com"
S3_BUCKET_NAME = "tmp-transfer2"
S3_PREFIX = "telegram/"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/incremental-crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 初始化 S3 客户端
s3_client = boto3.client(
    "s3",
    aws_access_key_id=S3_ACCESS_KEY,
    aws_secret_access_key=S3_SECRET_KEY,
    endpoint_url=S3_ENDPOINT_URL,
    config=Config(
        signature_version="s3v4",
        connect_timeout=10,  # 连接超时10秒
        read_timeout=30,     # 读取超时30秒
        retries={'max_attempts': 2}
    ),
    region_name="auto",
)

@dataclass
class ChannelStatus:
    """频道状态跟踪"""
    name: str
    s3_key: str
    latest_message_id: int = 0
    completed: bool = False
    retry_count: int = 0
    in_progress: bool = False
    tried_accounts: set = None  # 记录已尝试的账号ID

    def __post_init__(self):
        if self.tried_accounts is None:
            self.tried_accounts = set()

class AccountPool:
    """账户池管理器 - 从account目录扫描可用账号"""
    
    def __init__(self):
        self.accounts = []
        self.account_lock = Lock()
        self.load_accounts_from_directory()
    
    def load_accounts_from_directory(self):
        """从account目录扫描所有可用的Telegram账号"""
        account_dirs = [
            "account/tg_account_0807_2",
            "account/tg_account_0807_20",
            "account/tg_account_0808_50"
        ]

        for account_dir in account_dirs:
            if os.path.exists(account_dir):
                self._scan_account_directory(account_dir)

        logger.info(f"从account目录加载了 {len(self.accounts)} 个账号")

    def _scan_account_directory(self, directory: str):
        """扫描单个账号目录"""
        # 查找所有的zip文件和直接的账号目录
        account_subdirs = [d for d in os.listdir(directory)
                          if os.path.isdir(os.path.join(directory, d)) and d.isdigit()]


        # 处理直接的账号目录
        for subdir in account_subdirs:
            try:
                account_info = self._extract_account_from_directory(os.path.join(directory, subdir))
                if account_info:
                    self.accounts.append(account_info)
            except Exception as e:
                logger.warning(f"处理账号目录 {subdir} 失败: {e}")

    def _extract_account_from_directory(self, account_dir: str) -> Optional[Dict]:
        """从账号目录中提取账号信息"""
        try:
            # 查找session文件和json配置文件
            session_files = glob.glob(os.path.join(account_dir, "*.session"))
            json_files = glob.glob(os.path.join(account_dir, "*.json"))

            if not session_files or not json_files:
                return None

            session_file = session_files[0]
            json_file = json_files[0]

            # 读取配置文件
            with open(json_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            phone = config.get('phone', '')
            if not phone.startswith('+'):
                phone = '+' + phone

            api_id = config.get('app_id', 0)
            api_hash = config.get('app_hash', "")

            # 处理代理配置
            proxy = None
            if 'proxy' in config and config['proxy']:
                proxy_config = config['proxy']
                if len(proxy_config) >= 6:
                    proxy = {
                        'proxy_type': 'socks5' if proxy_config[0] == 3 else 'http',
                        'addr': proxy_config[1],
                        'port': proxy_config[2],
                        'username': proxy_config[4] if len(proxy_config) > 4 else None,
                        'password': proxy_config[5] if len(proxy_config) > 5 else None
                    }

            account_info = {
                'id': phone,
                'phone': phone,
                'api_id': api_id,
                'api_hash': api_hash,
                'proxy': proxy,
                'session_file': session_file,
                'in_use': False,
                'failure_count': 0,
                'config': config  # 保存完整配置以备后用
            }

            return account_info

        except Exception as e:
            logger.error(f"解析账号目录 {account_dir} 失败: {e}")
            return None

    def get_available_account(self, exclude_accounts=None):
        """获取可用账户"""
        if exclude_accounts is None:
            exclude_accounts = set()

        with self.account_lock:
            for account in self.accounts:
                if (not account['in_use'] and
                    account['failure_count'] < 3 and
                    not account.get('invalid_api', False) and
                    account['id'] not in exclude_accounts):
                    account['in_use'] = True
                    return account
            return None

    def release_account(self, account_id: str):
        """释放账户"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['in_use'] = False
                    break

    def mark_account_failed(self, account_id: str):
        """标记账户失败"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['failure_count'] += 1
                    account['in_use'] = False
                    break

    def mark_account_invalid_api(self, account_id: str):
        """标记账户API配置无效，永久移除"""
        with self.account_lock:
            for account in self.accounts:
                if account['id'] == account_id:
                    account['invalid_api'] = True
                    account['in_use'] = False
                    account['failure_count'] = 999  # 设置高失败次数确保不会再被使用
                    logger.warning(f"账户 {account_id} API配置无效，已永久移除")
                    break

    def get_valid_accounts_count(self):
        """获取有效账户数量"""
        with self.account_lock:
            return len([acc for acc in self.accounts if not acc.get('invalid_api', False) and acc['failure_count'] < 3])

class S3ChannelManager:
    """S3频道管理器 - 从channels_completed.txt读取频道列表，然后在S3中查找对应文件"""

    def __init__(self):
        self.channels_status = {}
        self.channel_lock = Lock()
        self.date_prefixes = self._generate_date_prefixes()

    def _generate_date_prefixes(self) -> List[str]:
        """生成日期前缀列表，用于在S3中查找文件"""
        from datetime import datetime, timedelta

        prefixes = []
        # 生成最近30天的日期前缀
        for i in range(1, 30):
            date = datetime.now() - timedelta(days=i)
            prefix = f"telegram/{date.strftime('%m%d')}"
            prefixes.append(prefix)

        logger.info(f"生成了 {len(prefixes)} 个日期前缀: {prefixes[:5]}...")
        return prefixes

    def load_channels_from_completed_file(self) -> Dict[str, ChannelStatus]:
        """从channels_completed.txt读取频道列表，然后在S3中查找对应文件"""
        try:
            # 读取频道列表
            channels_file = "stable/channels_completed.txt"
            if not os.path.exists(channels_file):
                logger.error(f"频道文件不存在: {channels_file}")
                return {}

            logger.info(f"从 {channels_file} 读取频道列表...")
            with open(channels_file, 'r', encoding='utf-8') as f:
                channel_names = [line.strip() for line in f if line.strip()]

            logger.info(f"读取到 {len(channel_names)} 个频道")

            # 限制处理数量
            if len(channel_names) > MAX_CHANNELS_TO_PROCESS:
                channel_names = channel_names[:MAX_CHANNELS_TO_PROCESS]
                logger.info(f"限制处理前 {MAX_CHANNELS_TO_PROCESS} 个频道")

            processed_count = 0

            # 为每个频道在S3中查找对应文件
            for i, channel_name in enumerate(channel_names):
                if not channel_name:
                    continue

                # 确保频道名以@开头
                # if not channel_name.startswith('@'):
                #     channel_name = '@' + channel_name

                logger.info(f"处理频道 {i+1}/{len(channel_names)}: {channel_name}")

                # 在S3中查找该频道的文件
                s3_key, latest_id = self._find_channel_in_s3(channel_name)

                if s3_key and latest_id > 0:
                    self.channels_status[channel_name] = ChannelStatus(
                        name=channel_name,
                        s3_key=s3_key,
                        latest_message_id=latest_id
                    )
                    processed_count += 1
                    logger.info(f"找到频道 {channel_name}: {s3_key} (最新消息ID: {latest_id})")
                else:
                    logger.warning(f"未找到频道 {channel_name} 的S3文件")

            logger.info(f"成功加载了 {processed_count} 个频道")
            return self.channels_status

        except Exception as e:
            logger.error(f"从频道文件加载失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {}

    def _find_channel_in_s3(self, channel_name: str) -> Tuple[Optional[str], int]:
        """在S3中查找指定频道的文件"""
        # 移除@符号用于文件名匹配
        clean_name = channel_name.replace('@', '')

        # 在各个日期前缀下查找
        for prefix in self.date_prefixes:
            try:
                # 构造可能的文件名
                possible_key = f"{prefix}/{clean_name}_messages.json"

                # 检查文件是否存在
                try:
                    s3_client.head_object(Bucket=S3_BUCKET_NAME, Key=possible_key)
                    # 文件存在，获取最新消息ID
                    latest_id = self._get_latest_message_id_from_s3(possible_key)
                    if latest_id > 0:
                        return possible_key, latest_id
                except s3_client.exceptions.NoSuchKey:
                    # 文件不存在，继续查找下一个前缀
                    continue
                except Exception as e:
                    logger.warning(f"检查文件 {possible_key} 时出错: {e}")
                    continue

            except Exception as e:
                logger.warning(f"在前缀 {prefix} 下查找频道 {channel_name} 时出错: {e}")
                continue

        return None, 0
    
    def _extract_channel_name_from_key(self, s3_key: str) -> Optional[str]:
        """从S3 key中提取频道名"""
        try:
            filename = os.path.basename(s3_key)
            if filename.endswith("_messages.json"):
                channel_name = filename[:-len("_messages.json")]
                # 如果不是以@开头，添加@
                # if not channel_name.startswith('@'):
                #     channel_name = '@' + channel_name
                return channel_name
        except Exception as e:
            logger.error(f"解析频道名失败 {s3_key}: {e}")
        return None
    
    def _get_latest_message_id_from_s3(self, s3_key: str) -> int:
        """从S3文件中获取最新消息ID（读取第一行）"""
        try:
            # 先尝试读取较小的块来查找第一行
            chunk_size = 2048  # 2KB应该足够包含第一条消息

            try:
                response = s3_client.get_object(
                    Bucket=S3_BUCKET_NAME,
                    Key=s3_key,
                    Range=f'bytes=0-{chunk_size-1}'
                )
                content = response['Body'].read()

                # 尝试解码，如果失败则读取更多内容
                try:
                    content_str = content.decode('utf-8')
                except UnicodeDecodeError:
                    # 如果解码失败，可能是截断了多字节字符，读取更多内容
                    response = s3_client.get_object(
                        Bucket=S3_BUCKET_NAME,
                        Key=s3_key,
                        Range=f'bytes=0-{chunk_size*2-1}'
                    )
                    content = response['Body'].read()
                    content_str = content.decode('utf-8', errors='ignore')

                # 查找第一个完整的JSON行
                lines = content_str.split('\n')
                for line in lines:
                    line = line.strip().rstrip(',')
                    if line and line.startswith('{'):
                        try:
                            first_message = json.loads(line)
                            message_id = first_message.get('message_id', 0)
                            if message_id > 0:
                                return message_id
                        except json.JSONDecodeError:
                            continue

            except Exception:
                # 如果Range请求失败，回退到读取完整文件
                pass

            # 如果上面的方法都失败了，尝试读取完整文件
            try:
                response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
                full_content = response['Body'].read().decode('utf-8', errors='ignore')

                # 首先尝试按行格式解析
                lines = full_content.strip().split('\n')
                if lines and lines[0].strip():
                    first_line = lines[0].rstrip(',').strip()
                    if first_line:
                        try:
                            first_message = json.loads(first_line)
                            message_id = first_message.get('message_id', 0)
                            if message_id > 0:
                                return message_id
                        except json.JSONDecodeError:
                            pass

                # 如果按行解析失败，尝试解析完整JSON
                try:
                    data = json.loads(full_content)
                    if 'messages' in data and len(data['messages']) > 0:
                        return data['messages'][0].get('message_id', 0)
                except json.JSONDecodeError:
                    pass

            except Exception as e:
                logger.warning(f"读取完整文件 {s3_key} 失败: {e}")

        except Exception as e:
            logger.warning(f"读取S3文件 {s3_key} 的最新消息ID失败: {e}")

        return 0
    
    def get_next_channel(self, account_pool: 'AccountPool') -> Optional[ChannelStatus]:
        """获取下一个需要处理的频道"""
        with self.channel_lock:
            for channel_status in self.channels_status.values():
                if (not channel_status.completed and
                    channel_status.retry_count < MAX_RETRIES_PER_CHANNEL and
                    not channel_status.in_progress):

                    # 检查是否还有未尝试的账号
                    valid_accounts = account_pool.get_valid_accounts_count()
                    tried_accounts_count = len(channel_status.tried_accounts)

                    if tried_accounts_count < valid_accounts:
                        channel_status.in_progress = True
                        return channel_status
            return None
    
    def mark_channel_completed(self, channel_name: str):
        """标记频道完成"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].completed = True
                self.channels_status[channel_name].in_progress = False
    
    def release_channel(self, channel_name: str):
        """释放频道（失败时调用）"""
        with self.channel_lock:
            if channel_name in self.channels_status:
                self.channels_status[channel_name].in_progress = False
                self.channels_status[channel_name].retry_count += 1

class TelegramIncrementalCrawler:
    """Telegram增量抓取器"""

    def __init__(self, account_pool: AccountPool, channel_manager: S3ChannelManager, worker_id: int):
        self.account_pool = account_pool
        self.channel_manager = channel_manager
        self.worker_id = worker_id
        self.client = None
        self.current_account = None

    async def create_client(self, account):
        """创建Telegram客户端"""
        try:
            client = TelegramClient(
                account['session_file'],
                account['api_id'],
                account['api_hash'],
                proxy=account['proxy']
            )
            await client.start(phone=account['phone'])
            logger.info(f"Worker {self.worker_id}: 客户端连接成功 {account['phone']}")
            return client
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Worker {self.worker_id}: 创建客户端失败 {e} {account['phone']}")

            # 检查是否是API配置无效的错误
            if "api_id/api_hash combination is invalid" in error_msg:
                logger.warning(f"Worker {self.worker_id}: 检测到API配置无效，标记账户 {account['phone']}")
                self.account_pool.mark_account_invalid_api(account['id'])
            else:
                # 其他类型的错误，标记为普通失败
                self.account_pool.mark_account_failed(account['id'])

            return None

    def get_entity_type(self, entity):
        """获取实体类型"""
        if isinstance(entity, User):
            return "user"
        elif isinstance(entity, Channel):
            return "channel"
        elif isinstance(entity, Chat):
            return "chat"
        return "unknown"

    async def scrape_channel_incremental_messages(self, channel_status: ChannelStatus) -> bool:
        """增量抓取频道消息"""
        channel_name = channel_status.name
        start_message_id = channel_status.latest_message_id

        try:
            entity = await self.client.get_entity(channel_name)

            # 创建输出目录
            os.makedirs('incremental_messages', exist_ok=True)
            file_name = f"{channel_name.replace('@', '').replace('/', '_')}_incremental_messages.json"
            file_path = f"incremental_messages/{file_name}"

            message_count = 0
            total_characters_en = 0
            total_characters_zh = 0
            logger.info(f"Worker {self.worker_id}: 开始增量抓取频道 {channel_name}，从消息ID {start_message_id} 开始")

            # 获取最新消息ID
            latest_msg = await self.client.get_messages(channel_name, limit=1)
            if not latest_msg:
                logger.info(f"{channel_name} 频道没有消息")
                return True

            latest_message_id = latest_msg[0].id

            # 如果没有新消息，直接返回成功
            if latest_message_id <= start_message_id:
                logger.info(f"Worker {self.worker_id}: 频道 {channel_name} 没有新消息")
                return True

            logger.info(f"Worker {self.worker_id}: 频道 {channel_name} 有新消息，从 {start_message_id} 到 {latest_message_id}")

            # 从start_message_id+1开始抓取到最新
            current_id = latest_message_id

            while current_id > start_message_id:
                # 从 current_id 往前取 page_size 条，但不超过 start_message_id
                msgs = await self.client.get_messages(channel_name, limit=PAGE_SIZE, max_id=current_id)
                if not msgs:
                    break

                # 过滤出新消息（ID > start_message_id）
                new_messages = [msg for msg in msgs if msg.id > start_message_id]
                if not new_messages:
                    break

                for message in new_messages:
                    if isinstance(message, MessageService) or not message.text:
                        continue

                    sender_user = ""
                    sender_username = ""
                    entity_type = "unknown"

                    if message.sender:
                        if hasattr(message.sender, 'first_name'):
                            sender_user = f"{message.sender.first_name or ''} {message.sender.last_name or ''}".strip()
                        if hasattr(message.sender, 'username') and message.sender.username:
                            sender_username = message.sender.username
                        entity_type = self.get_entity_type(message.sender)

                    if sender_username == channel_name.replace('@', ''):
                        continue

                    time_str = message.date.strftime('%Y-%m-%d %H:%M:%S') if message.date else ""

                    message_data = {
                        "message_id": message.id,
                        "sender_user": sender_user,
                        "sender_username": sender_username,
                        "message": message.text,
                        "time": time_str,
                        "entity_type": entity_type,
                        "channel": channel_name
                    }

                    # 按行追加写入JSON
                    with open(file_path, 'a', encoding='utf-8') as f:
                        json.dump(message_data, f, ensure_ascii=False)
                        f.write('\n')

                    message_count += 1
                    total_characters_en += count_en_words(message.text)
                    total_characters_zh += count_zh_chars(message.text)

                    if message_count % 100 == 0:
                        logger.info(f"Worker {self.worker_id}: 频道 {channel_name} 已处理 {message_count} 条新消息")

                # 更新current_id为最小的消息ID
                current_id = min(msg.id for msg in new_messages)
                await asyncio.sleep(random.uniform(10, 15))

            logger.info(f"Worker {self.worker_id}: 完成增量抓取频道 {channel_name}: {message_count} 条新消息")

            # 记录到完成文件
            with open("incremental_completed.txt", "a", encoding='utf-8') as f:
                f.write(f"{channel_name}\n")

            # 记录统计信息
            with open("incremental_statistics.txt", "a", encoding='utf-8') as f:
                f.write(f"{channel_name},{message_count},{total_characters_zh},{total_characters_en},{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            return True

        except errors.ChannelPrivateError:
            logger.warning(f"Worker {self.worker_id}: 频道 {channel_name} 是私有频道")
            return "channel_private"
        except errors.FloodWaitError as e:
            logger.warning(f"Worker {self.worker_id}: 触发速率限制，等待 {e.seconds} 秒")
            await asyncio.sleep(e.seconds)
            return "flood_wait"
        except ValueError as e:
            error_msg = str(e)
            if "No user has" in error_msg and "as username" in error_msg:
                logger.warning(f"Worker {self.worker_id}: 当前账号无法访问频道 {channel_name}: {e}")
                return "no_access"
            else:
                logger.error(f"Worker {self.worker_id}: 增量抓取频道 {channel_name} 失败: {e}")
                return False
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 增量抓取频道 {channel_name} 失败: {e}")
            return False

    async def process_channel(self, channel_status: ChannelStatus):
        """处理单个频道"""
        channel_name = channel_status.name

        # 获取可用账户，排除已尝试过的账户
        account = self.account_pool.get_available_account(exclude_accounts=channel_status.tried_accounts)
        if not account:
            logger.warning(f"Worker {self.worker_id}: 没有可用账户处理频道 {channel_name} (已尝试 {len(channel_status.tried_accounts)} 个账号)")
            return False

        # 记录尝试的账号
        channel_status.tried_accounts.add(account['id'])

        try:
            self.current_account = account

            # 创建客户端
            self.client = await self.create_client(account)
            if not self.client:
                # 创建客户端失败，create_client方法已经处理了账户标记
                logger.warning(f"Worker {self.worker_id}: 账户 {account['phone']} 创建客户端失败，已从账户池移除")
                return False

            # 增量抓取消息
            result = await self.scrape_channel_incremental_messages(channel_status)

            if result is True:
                self.channel_manager.mark_channel_completed(channel_name)
                return True
            elif result == "no_access":
                # 当前账号无法访问，但其他账号可能可以，不标记为失败
                logger.info(f"Worker {self.worker_id}: 账号 {account['phone']} 无法访问频道 {channel_name}，将尝试其他账号")
                return "retry_with_other_account"
            elif result == "channel_private":
                # 私有频道，所有账号都无法访问
                logger.warning(f"Worker {self.worker_id}: 频道 {channel_name} 是私有频道，跳过")
                self.channel_manager.mark_channel_completed(channel_name)  # 标记为完成，避免重复尝试
                return True
            elif result == "flood_wait":
                # 速率限制，稍后重试
                return "retry_later"
            else:
                # 其他错误
                return False

        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 处理频道 {channel_name} 时出错: {e}")
            self.account_pool.mark_account_failed(account['id'])
            return False
        finally:
            if self.client:
                await self.client.disconnect()
            self.account_pool.release_account(account['id'])

async def worker_thread(account_pool: AccountPool, channel_manager: S3ChannelManager, worker_id: int):
    """工作线程函数"""
    crawler = TelegramIncrementalCrawler(account_pool, channel_manager, worker_id)

    try:
        while True:
            # 检查是否还有可用账户
            valid_accounts = account_pool.get_valid_accounts_count()
            if valid_accounts == 0:
                logger.error(f"Worker {worker_id}: 没有可用账户，停止工作")
                break

            # 获取下一个需要处理的频道
            channel_status = channel_manager.get_next_channel(account_pool)
            if not channel_status:
                logger.info(f"Worker {worker_id}: 没有更多频道需要处理")
                break

            logger.info(f"Worker {worker_id}: 开始处理频道 {channel_status.name}，最新消息ID: {channel_status.latest_message_id} (剩余有效账户: {valid_accounts})")

            try:
                result = await crawler.process_channel(channel_status)

                if result is True:
                    # 成功完成
                    logger.info(f"Worker {worker_id}: 频道 {channel_status.name} 处理成功")
                elif result == "retry_with_other_account":
                    # 需要换账号重试
                    logger.info(f"Worker {worker_id}: 频道 {channel_status.name} 需要换账号重试")
                    channel_manager.release_channel(channel_status.name)
                elif result == "retry_later":
                    # 稍后重试
                    logger.info(f"Worker {worker_id}: 频道 {channel_status.name} 稍后重试")
                    channel_manager.release_channel(channel_status.name)
                else:
                    # 处理失败
                    logger.warning(f"Worker {worker_id}: 频道 {channel_status.name} 处理失败")
                    channel_manager.release_channel(channel_status.name)
            except Exception as e:
                logger.error(f"Worker {worker_id}: 处理频道 {channel_status.name} 时出错: {e}")
                channel_manager.release_channel(channel_status.name)

            # 随机延迟
            await asyncio.sleep(random.uniform(15, 30))

    except Exception as e:
        logger.error(f"Worker {worker_id}: 工作线程出错: {e}")

async def main():
    try:
        logger.info("启动Telegram频道增量消息抓取器")

        # 创建必要的目录
        os.makedirs('incremental_messages', exist_ok=True)
        os.makedirs('logs', exist_ok=True)

        # 初始化账户池
        logger.info("正在初始化账户池...")
        account_pool = AccountPool()

        if not account_pool.accounts:
            logger.error("没有可用的账号")
            return

        # 初始化S3频道管理器并加载频道
        logger.info("正在初始化S3频道管理器...")
        channel_manager = S3ChannelManager()

        logger.info("开始从channels_completed.txt加载频道信息，这可能需要一些时间...")
        channels_status = channel_manager.load_channels_from_completed_file()

        if not channels_status:
            logger.error("没有需要处理的频道")
            return

        valid_accounts = account_pool.get_valid_accounts_count()
        logger.info(f"账户池: {len(account_pool.accounts)} 个账号 (有效: {valid_accounts})")
        logger.info(f"频道池: {len(channels_status)} 个频道")

        # 创建工作线程
        thread_count = min(THREAD_COUNT, valid_accounts)
        if thread_count == 0:
            logger.error("没有有效的账号可以使用")
            return
        tasks = []

        for i in range(thread_count):
            task = asyncio.create_task(worker_thread(account_pool, channel_manager, i + 1))
            tasks.append(task)
            logger.info(f"启动工作线程 {i + 1}")

        # 等待所有任务完成
        await asyncio.gather(*tasks)

        logger.info("所有增量抓取任务完成")

        # 输出统计信息
        completed_count = sum(1 for status in channels_status.values() if status.completed)
        total_count = len(channels_status)
        logger.info(f"处理完成: {completed_count}/{total_count} 个频道")

        # 清理临时文件
        for account in account_pool.accounts:
            if 'temp_dir' in account and os.path.exists(account['temp_dir']):
                try:
                    import shutil
                    shutil.rmtree(account['temp_dir'])
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {e}")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(main())
